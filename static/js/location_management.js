//location_management.js

// Utility functions - define these at the very top, outside any event listeners
function getCSRFToken() {
  // Try getting from meta tag first
  const metaToken = document.querySelector('meta[name="csrf-token"]')?.content;
  if (metaToken) return metaToken;

  // Try getting from hidden input
  const inputToken = document.querySelector('input[name="csrf_token"]')?.value;
  if (inputToken) return inputToken;

  // Try getting from cookie
  const cookie = document.cookie
    .split("; ")
    .find((row) => row.startsWith("csrf_token="));
  if (cookie) return cookie.split("=")[1];

  throw new Error("CSRF token not found");
}

async function makeRequest(url, method, data) {
  try {
    // Always get a fresh CSRF token from the form
    const csrfTokenElement = document.querySelector('input[name="csrf_token"]');
    if (!csrfTokenElement) {
      throw new Error("CSRF token not found in the form");
    }
    const csrfToken = csrfTokenElement.value;

    const headers = {
      "Content-Type": "application/json",
      "X-Requested-With": "XMLHttpRequest",
    };

    // Add CSRF token to headers for all non-GET requests
    if (method !== "GET") {
      headers["X-CSRFToken"] = csrfToken;
    }

    const options = {
      method: method,
      headers: headers,
      credentials: "same-origin",
    };

    // For non-GET requests, include the CSRF token in both body and headers
    if (method !== "GET" && data) {
      options.body = JSON.stringify({
        ...data,
        csrf_token: csrfToken,
      });
    }

    console.log(`Making ${method} request to ${url}`);
    const response = await fetch(url, options);
    const contentType = response.headers.get("content-type");

    if (!response.ok) {
      if (contentType && contentType.includes("application/json")) {
        const errorData = await response.json();
        console.error("API error response:", errorData);

        // Log the complete error response for debugging
        console.log(
          "Complete error response:",
          JSON.stringify(errorData, null, 2)
        );

        // Preserve the original error structure for more detailed handling
        const error = new Error(errorData.message || "Request failed");
        error.status = response.status;
        error.errors =
          errorData.errors || errorData.error || errorData.validation_errors;
        throw error;
      } else {
        const errorText = await response.text();
        console.error("API error (non-JSON):", errorText);
        throw new Error(
          errorText || `Request failed with status ${response.status}`
        );
      }
    }

    if (contentType && contentType.includes("application/json")) {
      return response.json();
    } else {
      return { success: true };
    }
  } catch (error) {
    console.error("Request error:", error);
    throw error;
  }
}

// Helper functions
function showError(message) {
  Swal.fire({
    title: "Error!",
    text: message,
    icon: "error",
    confirmButtonColor: "#DC2626",
    timer: 5000,
    timerProgressBar: true,
  });
}

function showSuccess(message) {
  Swal.fire({
    title: "Success!",
    text: message,
    icon: "success",
    confirmButtonColor: "#10B981",
    timer: 3000,
    timerProgressBar: true,
  });
}

// User data handling
function getUserInitials(email, name) {
  // Try to get initials from name first if available
  if (name && name.trim()) {
    const nameParts = name.trim().split(/\s+/);
    if (nameParts.length >= 2) {
      return (nameParts[0][0] + nameParts[1][0]).toUpperCase();
    } else if (nameParts.length === 1) {
      return nameParts[0].substring(0, 2).toUpperCase();
    }
  }

  // Fallback to email
  if (email && email.includes("@")) {
    const username = email.split("@")[0];
    return username.substring(0, Math.min(3, username.length)).toLowerCase();
  }

  // Default fallback
  return "sys";
}

// Initialize user data
async function initializeUserData() {
  const updatedByField = document.getElementById("updated-by");

  // Set default value
  updatedByField.value = "sys";

  try {
    const response = await fetch("/api/current_user", {
      method: "GET",
      headers: {
        "X-Requested-With": "XMLHttpRequest",
        Accept: "application/json",
      },
      credentials: "same-origin",
    });

    if (!response.ok) {
      if (response.status === 401) {
        console.warn("User not authenticated, using default value");
      } else {
        console.warn(
          `User API returned status ${response.status}, using default value`
        );
      }
      return;
    }

    const data = await response.json();

    if (data.success && data.user) {
      const user = data.user;
      const initials = getUserInitials(user.email, user.name);
      updatedByField.value = initials;

      // You could store the user data for later use
      window.currentUser = user;

      console.log(`User initialized: ${user.name} (${initials})`);
    }
  } catch (error) {
    console.error("Error fetching user info:", error);
    // Keep the default value already set
  }
}

// Form validation with detailed error reporting
function validateForm() {
  // Clear previous validation errors
  document.querySelectorAll(".error-message").forEach((el) => el.remove());
  document.querySelectorAll(".border-red-500").forEach((el) => {
    el.classList.remove("border-red-500");
  });

  const toggle = document.getElementById("add-new-location-toggle");
  const isEditing = document.getElementById("is-editing").value === "true";
  let locationField;
  let errors = [];

  // Validate location name/selection
  if (toggle.checked) {
    // Adding new location
    locationField = document.getElementById("location-name-input");
    if (!locationField.value.trim()) {
      markFieldError(locationField, "Location name is required");
      errors.push("Location name is required");
    } else if (locationField.value.length < 2) {
      markFieldError(
        locationField,
        "Location name must be at least 2 characters"
      );
      errors.push("Location name must be at least 2 characters");
    }
  } else if (isEditing) {
    // Editing existing location label
    locationField = document.getElementById("location-label-edit");
    if (!locationField.value.trim()) {
      markFieldError(locationField, "Location label is required");
      errors.push("Location label is required");
    } else if (locationField.value.length < 2) {
      markFieldError(
        locationField,
        "Location label must be at least 2 characters"
      );
      errors.push("Location label must be at least 2 characters");
    }
  } else {
    // Using existing location
    locationField = document.getElementById("location-name-dropdown");
    if (!locationField.value) {
      markFieldError(locationField, "Please select a location");
      errors.push("Please select a location");
    }
  }

  // Validate required fields
  const requiredFields = [
    { id: "shipping-address", label: "Address" },
    { id: "contact-person", label: "Contact person" },
    { id: "phone", label: "Phone number" },
    { id: "email", label: "Email address" },
  ];

  for (const field of requiredFields) {
    const element = document.getElementById(field.id);
    if (!element.value.trim()) {
      markFieldError(element, `${field.label} is required`);
      errors.push(`${field.label} is required`);
    }
  }

  // Validate email
  const emailField = document.getElementById("email");
  if (emailField.value.trim()) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(emailField.value.trim())) {
      markFieldError(emailField, "Please enter a valid email address");
      errors.push("Invalid email format");
    }
  }

  // Validate phone number
  const phoneField = document.getElementById("phone");
  if (phoneField.value.trim()) {
    // Basic phone validation - adjust regex as needed
    const phoneRegex = /^[\d\s\-+()]{6,20}$/;
    if (!phoneRegex.test(phoneField.value.trim())) {
      markFieldError(phoneField, "Please enter a valid phone number");
      errors.push("Invalid phone number format");
    }
  }

  // If there are errors, show the first one
  if (errors.length > 0) {
    showError(errors[0]);
    return false;
  }

  return true;
}

// Helper function to mark a field with an error
function markFieldError(field, message) {
  field.classList.add("border-red-500");

  // Add error message below the field
  const errorDiv = document.createElement("div");
  errorDiv.className = "text-red-500 text-sm mt-1 error-message";
  errorDiv.textContent = message;

  // Insert after the field
  field.parentNode.insertBefore(errorDiv, field.nextSibling);
}

// Get form data for submission
function getFormData() {
  const toggle = document.getElementById("add-new-location-toggle");
  const isEditing = document.getElementById("is-editing").value === "true";

  let locationName;
  if (toggle.checked) {
    // Adding new location
    locationName = document.getElementById("location-name-input").value.trim();
  } else if (isEditing) {
    // Editing existing location - get from edit field
    locationName = document.getElementById("location-label-edit").value.trim();
  } else {
    // Viewing existing location - get from dropdown
    locationName = document.getElementById("location-name-dropdown").value;
  }

  const formData = {
    location_id: locationName,
    label: locationName,
    address: document.getElementById("shipping-address").value.trim(),
    email: document.getElementById("email").value.trim(),
    telephone: document.getElementById("phone").value.trim(),
    contact_person: document.getElementById("contact-person").value.trim(),
    updated_by: document.getElementById("updated-by").value || "sys",
  };

  // Add original location ID for modifications
  const originalLocationId = document.getElementById("original-location-id").value;
  if (originalLocationId && isEditing) {
    formData.original_location_id = originalLocationId;
  }

  return formData;
}

function clearForm() {
  document.getElementById("locationForm").reset();
  const toggle = document.getElementById("add-new-location-toggle");
  toggle.checked = false;

  // Reset all input field visibility
  document.getElementById("location-name-dropdown").classList.remove("hidden");
  document.getElementById("location-name-input").classList.add("hidden");
  document.getElementById("location-label-edit").classList.add("hidden");

  // Reset edit mode
  setEditMode(false);

  // Clear hidden fields
  document.getElementById("original-location-id").value = "";
  document.getElementById("is-editing").value = "false";

  // Clear validation styles
  document.querySelectorAll(".border-red-500").forEach((el) => {
    el.classList.remove("border-red-500");
  });

  // Remove error messages
  document.querySelectorAll(".error-message").forEach((el) => {
    el.remove();
  });
}

// Set edit mode state and update UI accordingly
function setEditMode(isEditing) {
  const editButton = document.getElementById("editLabelButton");
  const saveButton = document.getElementById("saveButton");
  const cancelButton = document.getElementById("cancelEditButton");
  const modifyButton = document.getElementById("modifyButton");
  const addButton = document.getElementById("addButton");
  const deleteButton = document.getElementById("deleteButton");

  document.getElementById("is-editing").value = isEditing.toString();

  if (isEditing) {
    // Show edit mode buttons
    editButton.classList.add("hidden");
    saveButton.classList.remove("hidden");
    cancelButton.classList.remove("hidden");

    // Hide other action buttons during edit
    modifyButton.classList.add("hidden");
    addButton.classList.add("hidden");
    deleteButton.classList.add("hidden");

    // Make form fields editable
    enableFormEditing(true);
  } else {
    // Show normal mode buttons
    editButton.classList.add("hidden");
    saveButton.classList.add("hidden");
    cancelButton.classList.add("hidden");

    // Show other action buttons
    modifyButton.classList.remove("hidden");
    addButton.classList.remove("hidden");
    deleteButton.classList.remove("hidden");

    // Make form fields read-only
    enableFormEditing(false);
  }
}

// Enable/disable form editing
function enableFormEditing(enabled) {
  const fields = [
    "shipping-address",
    "contact-person",
    "phone",
    "email",
    "asana-link"
  ];

  fields.forEach(fieldId => {
    const field = document.getElementById(fieldId);
    if (field) {
      field.readOnly = !enabled;
      if (enabled) {
        field.classList.remove("bg-gray-100", "dark:bg-gray-600");
        field.classList.add("bg-white", "dark:bg-gray-700");
      } else {
        field.classList.add("bg-gray-100", "dark:bg-gray-600");
        field.classList.remove("bg-white", "dark:bg-gray-700");
      }
    }
  });
}

// Toggle label editing mode
function toggleLabelEdit() {
  const dropdown = document.getElementById("location-name-dropdown");
  const editInput = document.getElementById("location-label-edit");
  const editButton = document.getElementById("editLabelButton");

  if (dropdown.classList.contains("hidden")) {
    // Currently in edit mode, switch back to dropdown
    editInput.classList.add("hidden");
    dropdown.classList.remove("hidden");
    editButton.innerHTML = '<i class="fas fa-edit mr-2"></i>Edit Label';
    setEditMode(false);
  } else {
    // Switch to edit mode
    const currentLabel = dropdown.options[dropdown.selectedIndex]?.text || "";
    editInput.value = currentLabel;
    dropdown.classList.add("hidden");
    editInput.classList.remove("hidden");
    editButton.innerHTML = '<i class="fas fa-eye mr-2"></i>View Only';
    setEditMode(true);
    editInput.focus();
  }
}

// Save location changes
async function saveLocation() {
  if (!validateForm()) {
    return;
  }

  const originalLocationId = document.getElementById("original-location-id").value;
  if (!originalLocationId) {
    showError("No location selected for modification");
    return;
  }

  // Show loading state
  const saveButton = document.getElementById("saveButton");
  const originalButtonText = saveButton.innerHTML;
  saveButton.disabled = true;
  saveButton.innerHTML = `
    <svg class="animate-spin h-5 w-5 mr-2 inline" viewBox="0 0 24 24">
      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
    Saving...
  `;

  try {
    const formData = getFormData();
    console.log("Saving location data:", formData);

    const response = await makeRequest("/api/location/modify", "PUT", formData);

    if (response.success) {
      showSuccess("Location saved successfully");

      // Update the dropdown with new location ID if it changed
      const newLocationId = response.new_location_id || formData.location_id;

      // Refresh the dropdown and select the updated location
      await loadLocationDropdown();

      // Select the updated location in dropdown
      const dropdown = document.getElementById("location-name-dropdown");
      dropdown.value = newLocationId;

      // Exit edit mode and reload the location data
      setEditMode(false);
      await viewLocation();

    } else {
      showError(response.message || "Failed to save location");
    }
  } catch (error) {
    console.error("Error saving location:", error);

    if (error.errors && Array.isArray(error.errors)) {
      const errorList = error.errors.join(", ");
      showError(`Validation failed: ${errorList}`);
    } else if (error.errors && typeof error.errors === "object") {
      const errorMessages = Object.values(error.errors).flat();
      showError(`Validation failed: ${errorMessages.join(", ")}`);
    } else {
      showError(`Error saving location: ${error.message}`);
    }
  } finally {
    // Restore button state
    saveButton.disabled = false;
    saveButton.innerHTML = originalButtonText;
  }
}

// Cancel edit mode
function cancelEdit() {
  const originalLocationId = document.getElementById("original-location-id").value;
  if (originalLocationId) {
    // Reload the original location data
    const dropdown = document.getElementById("location-name-dropdown");
    dropdown.value = originalLocationId;
    viewLocation();
  } else {
    clearForm();
  }
  setEditMode(false);
}

// Main functionality functions
async function loadLocationDropdown() {
  try {
    const response = await makeRequest("/api/locations", "GET");
    const dropdown = document.getElementById("location-name-dropdown");
    dropdown.innerHTML = '<option value="">Select Location</option>';

    // Access the locations array from the response
    if (response.success && response.locations) {
      response.locations.forEach((location) => {
        const option = document.createElement("option");
        option.value = location.location_id;
        option.textContent = location.label;
        dropdown.appendChild(option);
      });
    } else {
      throw new Error("No locations data received");
    }
  } catch (error) {
    console.error("Error loading locations:", error);
    const dropdown = document.getElementById("location-name-dropdown");
    dropdown.innerHTML = '<option value="">Error loading locations</option>';
    showError(`Error loading locations: ${error.message}`);
  }
}

// addLocation function
async function addLocation() {
  // Validate form first
  if (!validateForm()) {
    return;
  }

  // Show loading state
  const addButton = document.getElementById("addButton");
  const originalButtonText = addButton.innerHTML;
  addButton.disabled = true;
  addButton.innerHTML = `
    <svg class="animate-spin h-5 w-5 mr-2 inline" viewBox="0 0 24 24">
      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
    Adding...
  `;

  try {
    // Get form data
    const formData = getFormData();

    console.log("Submitting location data:", formData);

    // Add debug info to see what's being submitted
    console.log("Form data details:", {
      location_id: formData.location_id,
      label: formData.label,
      address: formData.address,
      email: formData.email,
      telephone: formData.telephone,
      contact_person: formData.contact_person,
      updated_by: formData.updated_by,
    });

    // Send the request
    const result = await makeRequest("/api/location/add", "POST", formData);

    if (result.success) {
      showSuccess("Location added successfully");

      // Refresh the dropdown and clear the form
      await loadLocationDropdown();
      clearForm();
    } else {
      showError(result.message || "Failed to add location");
    }
  } catch (error) {
    console.error("Error adding location:", error);

    // Improved error handling for validation errors
    if (error.errors && Array.isArray(error.errors)) {
      // Display all validation errors
      const errorList = error.errors.join(", ");
      showError(`Validation failed: ${errorList}`);
    } else if (error.errors && typeof error.errors === "object") {
      // Handle case where errors is an object with field names as keys
      const errorMessages = Object.values(error.errors).flat();
      showError(`Validation failed: ${errorMessages.join(", ")}`);
    } else {
      showError(`Error adding location: ${error.message}`);
    }
  } finally {
    // Restore button state
    addButton.disabled = false;
    addButton.innerHTML = originalButtonText;
  }
}

async function modifyLocation() {
  if (!validateForm()) return;

  const locationName = document.getElementById("location-name-dropdown").value;
  if (!locationName) {
    showError("Please select a location to modify");
    return;
  }

  const result = await Swal.fire({
    title: "Modify Location",
    text: "Are you sure you want to modify this location?",
    icon: "question",
    showCancelButton: true,
    confirmButtonColor: "#FBBF24",
    cancelButtonColor: "#6B7280",
    confirmButtonText: "Yes, modify it",
    cancelButtonText: "Cancel",
  });

  if (result.isConfirmed) {
    try {
      const formData = getFormData();

      const response = await makeRequest(
        "/api/location/modify",
        "PUT",
        formData
      );

      if (response.success) {
        showSuccess("Location modified successfully");
        await loadLocationDropdown();
        clearForm();
      } else {
        showError(response.message || "Failed to modify location");
      }
    } catch (error) {
      showError(`Error modifying location: ${error.message}`);
    }
  }
}

// Delete location function
async function deleteLocation() {
  const locationId = document.getElementById("location-name-dropdown").value;
  if (!locationId) {
    showError("Please select a location to delete");
    return;
  }

  const result = await Swal.fire({
    title: "Delete Location",
    text: "Are you sure you want to delete this location?",
    icon: "warning",
    showCancelButton: true,
    confirmButtonColor: "#DC2626",
    cancelButtonColor: "#6B7280",
    confirmButtonText: "Yes, delete it",
    cancelButtonText: "Cancel",
  });

  if (result.isConfirmed) {
    try {
      const response = await makeRequest("/api/location/delete", "DELETE", {
        location_id: locationId, // Fixed parameter name
      });

      if (response.success) {
        showSuccess("Location deleted successfully");
        await loadLocationDropdown();
        clearForm();
      } else {
        showError(response.message || "Failed to delete location");
      }
    } catch (error) {
      showError(`Error deleting location: ${error.message}`);
    }
  }
}

// View location function
async function viewLocation() {
  const locationId = document.getElementById("location-name-dropdown").value;
  if (!locationId) {
    showError("Please select a location to view");
    return;
  }

  try {
    const response = await makeRequest(
      `/api/location/view/${locationId}`,
      "GET"
    );

    if (response.success && response.location) {
      const location = response.location;

      // Populate form fields
      document.getElementById("shipping-address").value = location.address || "";
      document.getElementById("email").value = location.email || "";
      document.getElementById("phone").value = location.telephone || "";
      document.getElementById("contact-person").value = location.contact_person || "";

      const asanaLinkField = document.getElementById("asana-link");
      if (asanaLinkField) {
        asanaLinkField.value = location.asana_link || "";
      }

      // Store original location ID for editing
      document.getElementById("original-location-id").value = locationId;

      // Show edit label button and enable form editing controls
      const editLabelButton = document.getElementById("editLabelButton");
      editLabelButton.classList.remove("hidden");

      // Make form fields read-only initially
      enableFormEditing(false);

      // Exit any existing edit mode
      setEditMode(false);

    } else {
      throw new Error("Location data not found");
    }
  } catch (error) {
    showError(`Error viewing location: ${error.message}`);
  }
}

async function uploadFile() {
  const asanaLink = document.getElementById("asana-link").value.trim();
  if (!asanaLink) {
    showError("Please enter an Asana link");
    return;
  }

  try {
    const response = await makeRequest(
      `/api/get_task_info?asana_link=${encodeURIComponent(asanaLink)}`,
      "GET"
    );

    if (response.success && response.task_info) {
      const task_info = response.task_info;

      document.getElementById("shipping-address").value =
        task_info.address || "";
      document.getElementById("contact-person").value =
        task_info.contact_person || "";
      document.getElementById("phone").value = task_info.telephone || "";
      document.getElementById("email").value = task_info.email || "";
      document.getElementById("location-name-input").value =
        task_info.label || "";

      const toggle = document.getElementById("add-new-location-toggle");
      toggle.checked = true;
      toggle.dispatchEvent(new Event("change"));

      showSuccess("Form populated with Asana data");
    } else {
      showError(response.message || "Failed to get task information");
    }
  } catch (error) {
    showError(`Error fetching Asana data: ${error.message}`);
  }
}

// Function to upload from Asana
async function uploadFromAsana() {
  await uploadFile();
}

// Search functionality
// Add real-time search handling
function setupSearch() {
  const searchInput = document.getElementById("location-search");
  const clearButton = document.getElementById("search-clear");
  let searchTimeout;

  // Search function with debounce
  async function performSearch(searchTerm) {
    try {
      const response = await makeRequest(
        `/api/locations?search=${encodeURIComponent(searchTerm)}`,
        "GET"
      );
      const dropdown = document.getElementById("location-name-dropdown");

      if (response.success && response.locations) {
        // Clear existing options
        dropdown.innerHTML = "";

        if (response.locations.length === 0) {
          // If no locations found, show "No results" option
          const noResults = document.createElement("option");
          noResults.value = "";
          noResults.textContent = "No locations found";
          dropdown.appendChild(noResults);
        } else {
          // If search is empty, add "Select Location" option
          if (!searchTerm) {
            const defaultOption = document.createElement("option");
            defaultOption.value = "";
            defaultOption.textContent = "Select Location";
            dropdown.appendChild(defaultOption);
          }

          // Add matching locations
          response.locations.forEach((location) => {
            const option = document.createElement("option");
            option.value = location.location_id;
            option.textContent = location.label;
            dropdown.appendChild(option);
          });

          // If only one result found, automatically select it
          if (response.locations.length === 1) {
            dropdown.value = response.locations[0].location_id;
            // Trigger the change event to load location details
            dropdown.dispatchEvent(new Event("change"));
          }
        }
      }
    } catch (error) {
      showError(`Error searching locations: ${error.message}`);
    }
  }

  // Handle input changes with debounce
  searchInput.addEventListener("input", (e) => {
    const searchTerm = e.target.value.trim();
    clearButton.style.display = searchTerm ? "block" : "none";

    // Clear previous timeout
    clearTimeout(searchTimeout);

    // Set new timeout
    searchTimeout = setTimeout(() => {
      performSearch(searchTerm);
    }, 300); // Wait 300ms after user stops typing
  });

  // Handle enter key
  searchInput.addEventListener("keypress", (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      performSearch(searchInput.value.trim());
    }
  });

  // Handle clear button
  clearButton.addEventListener("click", () => {
    searchInput.value = "";
    clearButton.style.display = "none";
    loadLocationDropdown(); // Reset to show all locations
  });
}

// Export functionality
async function exportLocations() {
  try {
    const response = await fetch("/api/locations/export", {
      method: "GET",
      headers: {
        "X-Requested-With": "XMLHttpRequest",
      },
    });

    if (!response.ok) {
      throw new Error("Export failed");
    }

    // Get the filename from the Content-Disposition header if available
    const contentDisposition = response.headers.get("Content-Disposition");
    let filename = "locations_export.csv";
    if (contentDisposition) {
      const matches = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/.exec(
        contentDisposition
      );
      if (matches != null && matches[1]) {
        filename = matches[1].replace(/['"]/g, "");
      }
    }

    // Create blob from response
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);

    showSuccess("Export completed successfully");
  } catch (error) {
    console.error("Export error:", error);
    showError("Failed to export locations. Please try again.");
  }
}

// Keyboard shortcuts
function setupKeyboardShortcuts() {
  document.addEventListener("keydown", (e) => {
    if (e.ctrlKey || e.metaKey) {
      switch (e.key.toLowerCase()) {
        case "s":
          e.preventDefault();
          // Check if we're in edit mode, if so save, otherwise modify
          const isEditing = document.getElementById("is-editing").value === "true";
          if (isEditing) {
            saveLocation();
          } else {
            modifyLocation();
          }
          break;
        case "n":
          e.preventDefault();
          document.getElementById("add-new-location-toggle").click();
          break;
        case "e":
          e.preventDefault();
          exportLocations();
          break;
      }
    }
  });
}

// Initialize everything when the DOM is ready
document.addEventListener("DOMContentLoaded", async function () {
  const toggle = document.getElementById("add-new-location-toggle");
  const dropdown = document.getElementById("location-name-dropdown");
  const input = document.getElementById("location-name-input");

  // Initialize user data first
  await initializeUserData();

  // Toggle between dropdown and input field
  toggle.addEventListener("change", function () {
    if (toggle.checked) {
      dropdown.classList.add("hidden");
      input.classList.remove("hidden");
      input.required = true;
      dropdown.required = false;
    } else {
      dropdown.classList.remove("hidden");
      input.classList.add("hidden");
      input.required = false;
      dropdown.required = true;
    }
  });

  // Load dropdown options
  try {
    await loadLocationDropdown();
  } catch (error) {
    console.error("Error loading locations:", error);
    showError("Failed to load locations. Please try refreshing the page.");
  }

  // Add change event listener to location dropdown
  dropdown.addEventListener("change", function () {
    if (this.value) {
      viewLocation();
    } else {
      clearForm();
      // Hide edit label button when no location is selected
      document.getElementById("editLabelButton").classList.add("hidden");
    }
  });

  // Initialize other features
  setupSearch();
  setupKeyboardShortcuts();

  // Additional keyboard shortcuts
  document.addEventListener("keydown", function (e) {
    // Ctrl/Cmd + Enter to search
    if ((e.ctrlKey || e.metaKey) && e.key === "Enter") {
      e.preventDefault();
      viewLocation();
    }

    // Escape to clear form or cancel edit
    if (e.key === "Escape") {
      e.preventDefault();
      const isEditing = document.getElementById("is-editing").value === "true";
      if (isEditing) {
        cancelEdit();
      } else {
        clearForm();
      }
    }

    // F2 to toggle label edit mode (when location is selected)
    if (e.key === "F2") {
      e.preventDefault();
      const editButton = document.getElementById("editLabelButton");
      if (!editButton.classList.contains("hidden")) {
        toggleLabelEdit();
      }
    }
  });

  console.log("Location management interface initialized");
});
