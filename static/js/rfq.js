
document.addEventListener("DOMContentLoaded", function () {
  // DOM Elements - Form Fields
  const xfabCloudUrlInput = document.getElementById("xfabCloudUrl");
  const xfabCloudUrl2Input = document.getElementById("xfabCloudUrl2");
  const xfabCloudUrl3Input = document.getElementById("xfabCloudUrl3");
  const xfabCloudUrl4Input = document.getElementById("xfabCloudUrl4");
  const xfabCloudUrl5Input = document.getElementById("xfabCloudUrl5");

  // DOM Elements - Project Title Fields
  const sowTitle1Input = document.getElementById("sowTitle1");
  const sowTitle2Input = document.getElementById("sowTitle2");
  const sowTitle3Input = document.getElementById("sowTitle3");
  const sowTitle4Input = document.getElementById("sowTitle4");
  const sowTitle5Input = document.getElementById("sowTitle5");



  // DOM Elements - Form Actions
  const validateFormBtn = document.getElementById("validateFormBtn");
  const clearFormBtn = document.getElementById("clearFormBtn");
  const previewEmailBtn = document.getElementById("previewEmailBtn");

  // DOM Elements - Email Sending
  const sendSection = document.getElementById("sendSection");
  const sendAllBtn = document.getElementById("sendAllBtn");
  const clearSessionBtn = document.getElementById("clearSessionBtn");
  const resultsSection = document.getElementById("resultsSection");
  const loadingModal = document.getElementById("loadingModal");
  const loadingText = document.getElementById("loadingText");

  // DOM Elements - Preview Modal
  const emailPreviewModal = document.getElementById("emailPreviewModal");
  const closePreviewModal = document.getElementById("closePreviewModal");
  const previewSubject = document.getElementById("previewSubject");
  const previewTo = document.getElementById("previewTo");
  const previewCC = document.getElementById("previewCC");
  const emailBodyEditor = document.getElementById("emailBodyEditor");
  const refreshPreviewBtn = document.getElementById("refreshPreviewBtn");
  const sendFromPreviewBtn = document.getElementById("sendFromPreviewBtn");
  const cancelPreviewBtn = document.getElementById("cancelPreviewBtn");

  // State variables
  let currentMode = "production";

  // Check for existing data on page load
  let existingData = null;
  try {
    const existingDataElement = document.getElementById('existing-data');
    if (existingDataElement && existingDataElement.textContent.trim()) {
      existingData = JSON.parse(existingDataElement.textContent);
      console.log("Found existing RFQ data:", existingData);
      restoreFormData(existingData);
    }
  } catch (error) {
    console.log("No existing data found or error parsing:", error);
  }



  // Form event listeners
  validateFormBtn.addEventListener("click", validateAndPreview);
  clearFormBtn.addEventListener("click", clearForm);
  previewEmailBtn.addEventListener("click", showEmailPreview);

  // Preview modal event listeners
  closePreviewModal.addEventListener("click", hideEmailPreview);
  cancelPreviewBtn.addEventListener("click", hideEmailPreview);
  refreshPreviewBtn.addEventListener("click", refreshEmailPreview);
  sendFromPreviewBtn.addEventListener("click", sendEmailFromPreview);

  // Real-time validation - add event listeners to all form inputs
  xfabCloudUrlInput.addEventListener("input", checkFormCompletion);
  xfabCloudUrl2Input.addEventListener("input", checkFormCompletion);
  xfabCloudUrl3Input.addEventListener("input", checkFormCompletion);
  xfabCloudUrl4Input.addEventListener("input", checkFormCompletion);
  xfabCloudUrl5Input.addEventListener("input", checkFormCompletion);

  // Add event listeners for project title fields
  sowTitle1Input.addEventListener("input", checkFormCompletion);
  sowTitle2Input.addEventListener("input", checkFormCompletion);
  sowTitle3Input.addEventListener("input", checkFormCompletion);
  sowTitle4Input.addEventListener("input", checkFormCompletion);
  sowTitle5Input.addEventListener("input", checkFormCompletion);

  // Initial check on page load
  checkFormCompletion();

    // Email sending
    sendAllBtn.addEventListener("click", sendAllEmails);
    clearSessionBtn.addEventListener("click", clearSession);



  // Real-time form validation
  function checkFormCompletion() {
    // Collect current form data
    const data = {
      xfabCloudUrl: xfabCloudUrlInput.value.trim(),
      xfabCloudUrl2: xfabCloudUrl2Input.value.trim(),
      xfabCloudUrl3: xfabCloudUrl3Input.value.trim(),
      xfabCloudUrl4: xfabCloudUrl4Input.value.trim(),
      xfabCloudUrl5: xfabCloudUrl5Input.value.trim(),
      sowTitle1: sowTitle1Input.value.trim(),
      sowTitle2: sowTitle2Input.value.trim(),
      sowTitle3: sowTitle3Input.value.trim(),
      sowTitle4: sowTitle4Input.value.trim(),
      sowTitle5: sowTitle5Input.value.trim()
    };

    // Check which fields are missing
    const missingFields = [];
    if (!data.xfabCloudUrl) missingFields.push('SoW XFab Cloud URL (at least one required)');

    // Validate URL formats if provided
    let urlError = false;
    const urlFields = [
      { name: 'SoW XFab Cloud URL 1', value: data.xfabCloudUrl },
      { name: 'SoW XFab Cloud URL 2', value: data.xfabCloudUrl2 },
      { name: 'SoW XFab Cloud URL 3', value: data.xfabCloudUrl3 },
      { name: 'SoW XFab Cloud URL 4', value: data.xfabCloudUrl4 },
      { name: 'SoW XFab Cloud URL 5', value: data.xfabCloudUrl5 }
    ];

    const invalidUrls = [];
    urlFields.forEach(field => {
      if (field.value && !isValidUrl(field.value)) {
        invalidUrls.push(field.name);
        urlError = true;
      }
    });

    // Update form status
    if (missingFields.length === 0 && !urlError) {
      // All fields filled and valid - show send section
      showFormComplete();
      hideFormWarning();
    } else {
      // Missing fields or invalid URL - hide send section and show warning
      hideFormComplete();
      if (missingFields.length > 0) {
        showFormWarning(`Please fill in: ${missingFields.join(', ')}`);
      } else if (urlError) {
        showFormWarning(`Please enter valid URLs for: ${invalidUrls.join(', ')}`);
      }
    }
  }

  function showFormComplete() {
    if (sendSection) {
      sendSection.classList.remove("hidden");
    }
  }

  function hideFormComplete() {
    if (sendSection) {
      sendSection.classList.add("hidden");
    }
  }

  function showFormWarning(message) {
    // Create or update warning message
    let warningDiv = document.getElementById('formWarning');
    if (!warningDiv) {
      warningDiv = document.createElement('div');
      warningDiv.id = 'formWarning';
      warningDiv.className = 'bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-4';

      // Insert after the form
      const formContainer = document.querySelector('.bg-white.dark\\:bg-gray-800.rounded-lg.shadow-md.p-6');
      if (formContainer) {
        formContainer.appendChild(warningDiv);
      }
    }

    warningDiv.innerHTML = `
      <div class="flex items-center">
        <i class="fas fa-exclamation-triangle text-yellow-500 mr-2"></i>
        <span class="text-sm text-yellow-700">${message}</span>
      </div>
    `;
    warningDiv.classList.remove('hidden');
  }

  function hideFormWarning() {
    const warningDiv = document.getElementById('formWarning');
    if (warningDiv) {
      warningDiv.classList.add('hidden');
    }
  }

  // Form validation and preview functions (keep for manual validation if needed)
  function validateAndPreview() {
    // Just trigger the real-time validation
    checkFormCompletion();

    // Collect form data
    const data = {
      xfabCloudUrl: xfabCloudUrlInput.value.trim(),
      xfabCloudUrl2: xfabCloudUrl2Input.value.trim(),
      xfabCloudUrl3: xfabCloudUrl3Input.value.trim(),
      xfabCloudUrl4: xfabCloudUrl4Input.value.trim(),
      xfabCloudUrl5: xfabCloudUrl5Input.value.trim(),
      sowTitle1: sowTitle1Input.value.trim(),
      sowTitle2: sowTitle2Input.value.trim(),
      sowTitle3: sowTitle3Input.value.trim(),
      sowTitle4: sowTitle4Input.value.trim(),
      sowTitle5: sowTitle5Input.value.trim()
    };

    // Validate required fields
    const errors = [];
    if (!data.xfabCloudUrl) errors.push('At least one SoW XFab Cloud URL is required');

    // Validate URL formats
    const urlFields = [
      { name: 'XFab Cloud URL 1', value: data.xfabCloudUrl },
      { name: 'XFab Cloud URL 2', value: data.xfabCloudUrl2 },
      { name: 'XFab Cloud URL 3', value: data.xfabCloudUrl3 },
      { name: 'XFab Cloud URL 4', value: data.xfabCloudUrl4 },
      { name: 'XFab Cloud URL 5', value: data.xfabCloudUrl5 }
    ];

    const sowUrlFields = [
      { name: 'SoW XFab Cloud URL 1', value: data.xfabCloudUrl },
      { name: 'SoW XFab Cloud URL 2', value: data.xfabCloudUrl2 },
      { name: 'SoW XFab Cloud URL 3', value: data.xfabCloudUrl3 },
      { name: 'SoW XFab Cloud URL 4', value: data.xfabCloudUrl4 },
      { name: 'SoW XFab Cloud URL 5', value: data.xfabCloudUrl5 }
    ];

    sowUrlFields.forEach(field => {
      if (field.value && !isValidUrl(field.value)) {
        errors.push(`Please enter a valid ${field.name}`);
      }
    });

    if (errors.length > 0) {
      showError('Please fill in all required fields:\n' + errors.join('\n'));
      return;
    }

    // Show success message
    showSuccess('Form validated successfully! You can now send the RFQ email.');
  }

  function clearForm() {
    // Clear all form fields
    xfabCloudUrlInput.value = '';
    xfabCloudUrl2Input.value = '';
    xfabCloudUrl3Input.value = '';
    xfabCloudUrl4Input.value = '';
    xfabCloudUrl5Input.value = '';

    // Clear project title fields
    sowTitle1Input.value = '';
    sowTitle2Input.value = '';
    sowTitle3Input.value = '';
    sowTitle4Input.value = '';
    sowTitle5Input.value = '';



    // Hide send section and results
    sendSection.classList.add("hidden");
    resultsSection.classList.add("hidden");

    // Hide warning message
    hideFormWarning();

    // Clear stored data
    formData = {};

    // Trigger real-time validation to update UI
    checkFormCompletion();

    showSuccess('Form cleared successfully!');
  }

  // Restore form data from session
  function restoreFormData(data) {
    if (data.sifoProject) sifoProjectInput.value = data.sifoProject;
    if (data.talosLotId) talosLotIdInput.value = data.talosLotId;
    if (data.lotProject) lotProjectInput.value = data.lotProject;
    if (data.xfabCloudUrl) xfabCloudUrlInput.value = data.xfabCloudUrl;

    formData = data;
    sendSection.classList.remove("hidden");
  }

  // Utility functions
  function isValidUrl(string) {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  }

  // Email Preview Functions
  async function showEmailPreview() {
    // Collect current form data
    const formData = {
      xfabCloudUrl: xfabCloudUrlInput.value.trim(),
      xfabCloudUrl2: xfabCloudUrl2Input.value.trim(),
      xfabCloudUrl3: xfabCloudUrl3Input.value.trim(),
      xfabCloudUrl4: xfabCloudUrl4Input.value.trim(),
      xfabCloudUrl5: xfabCloudUrl5Input.value.trim(),
      sowTitle1: sowTitle1Input.value.trim(),
      sowTitle2: sowTitle2Input.value.trim(),
      sowTitle3: sowTitle3Input.value.trim(),
      sowTitle4: sowTitle4Input.value.trim(),
      sowTitle5: sowTitle5Input.value.trim()
    };

    // Validate required fields
    const errors = [];
    if (!formData.xfabCloudUrl) errors.push('At least one SoW XFab Cloud URL is required');

    if (errors.length > 0) {
      showError('Please fill in all required fields:\n' + errors.join('\n'));
      return;
    }

    try {
      // Show loading
      showLoading('Generating email preview...');

      // Generate preview
      const response = await fetch('/rfq/preview', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      });

      const result = await response.json();
      hideLoading();

      if (result.success) {
        // Populate preview modal
        previewSubject.value = result.preview.subject;
        previewTo.value = result.preview.recipients.to.join(', ');
        previewCC.value = result.preview.recipients.cc.join(', ');
        emailBodyEditor.innerHTML = result.preview.body;

        // Store form data for sending
        emailPreviewModal.dataset.formData = JSON.stringify(formData);

        // Show modal
        emailPreviewModal.classList.remove('hidden');
      } else {
        showError('Failed to generate preview: ' + result.message);
      }
    } catch (error) {
      hideLoading();
      showError('Error generating preview: ' + error.message);
    }
  }

  function hideEmailPreview() {
    emailPreviewModal.classList.add('hidden');
  }

  async function refreshEmailPreview() {
    // Get stored form data
    const formData = JSON.parse(emailPreviewModal.dataset.formData || '{}');

    try {
      showLoading('Refreshing preview...');

      const response = await fetch('/rfq/preview', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      });

      const result = await response.json();
      hideLoading();

      if (result.success) {
        // Update preview (but keep any manual edits to the body)
        previewSubject.value = result.preview.subject;
        previewTo.value = result.preview.recipients.to.join(', ');
        previewCC.value = result.preview.recipients.cc.join(', ');
        // Note: We don't update emailBodyEditor to preserve user edits

        showSuccess('Preview refreshed successfully!');
      } else {
        showError('Failed to refresh preview: ' + result.message);
      }
    } catch (error) {
      hideLoading();
      showError('Error refreshing preview: ' + error.message);
    }
  }

  async function sendEmailFromPreview() {
    try {
      // Get form data and edited content
      const formData = JSON.parse(emailPreviewModal.dataset.formData || '{}');
      const customSubject = previewSubject.value;
      const customBody = emailBodyEditor.innerHTML;

      // Prepare custom email data
      const emailData = {
        subject: customSubject,
        body: customBody,
        recipients: {
          to: previewTo.value.split(',').map(email => email.trim()),
          cc: previewCC.value.split(',').map(email => email.trim())
        },
        test_mode: currentMode === 'test'
      };

      showLoading('Sending custom email...');

      const response = await fetch('/rfq/send-custom', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(emailData)
      });

      const result = await response.json();
      hideLoading();

      if (result.success) {
        hideEmailPreview();
        showSuccess('Email sent successfully!');

        // Show results
        displayResults(result);
      } else {
        showError('Failed to send email: ' + result.message);
      }
    } catch (error) {
      hideLoading();
      showError('Error sending email: ' + error.message);
    }
  }

  function showError(message) {
    alert('Error: ' + message);
  }

  function showSuccess(message) {
    alert('Success: ' + message);
  }

  function showLoading(message) {
    if (loadingModal && loadingText) {
      loadingText.textContent = message;
      loadingModal.classList.remove("hidden");
    }
  }

  function hideLoading() {
    if (loadingModal) {
      loadingModal.classList.add("hidden");
    }
  }

  function showResults(data) {
    if (resultsSection) {
      resultsSection.classList.remove("hidden");
      // You can add more result display logic here if needed
      console.log("Email results:", data);
    }
  }





    function sendAllEmails() {
      // Collect current form data directly from inputs
      const currentFormData = {
        xfabCloudUrl: xfabCloudUrlInput.value.trim(),
        xfabCloudUrl2: xfabCloudUrl2Input.value.trim(),
        xfabCloudUrl3: xfabCloudUrl3Input.value.trim(),
        xfabCloudUrl4: xfabCloudUrl4Input.value.trim(),
        xfabCloudUrl5: xfabCloudUrl5Input.value.trim(),
        sowTitle1: sowTitle1Input.value.trim(),
        sowTitle2: sowTitle2Input.value.trim(),
        sowTitle3: sowTitle3Input.value.trim(),
        sowTitle4: sowTitle4Input.value.trim(),
        sowTitle5: sowTitle5Input.value.trim()
      };

      // Validate required fields before sending
      const errors = [];
      if (!currentFormData.xfabCloudUrl) errors.push('At least one SoW XFab Cloud URL is required');

      if (errors.length > 0) {
        showError('Please fill in all required fields:\n' + errors.join('\n'));
        return;
      }

      Swal.fire({
        title: "Send RFQ Email?",
        text: "Are you sure you want to send the RFQ email to XFab team?",
        icon: "question",
        showCancelButton: true,
        confirmButtonColor: "#28a745",
        cancelButtonColor: "#6c757d",
        confirmButtonText: "Yes, Send Email!",
        cancelButtonText: "Cancel",
      }).then((result) => {
        if (result.isConfirmed) {
          showLoading("Sending RFQ email...");

          // Prepare form data for sending
          const emailData = new FormData();
          emailData.append('sifoProject', currentFormData.sifoProject);
          emailData.append('talosLotId', currentFormData.talosLotId);
          emailData.append('lotProject', currentFormData.lotProject);
          emailData.append('xfabCloudUrl', currentFormData.xfabCloudUrl);
          emailData.append('mode', currentMode);



          fetch("/rfq/send", {
            method: "POST",
            body: emailData
          })
            .then((response) => response.json())
            .then((data) => {
              hideLoading();
              showResults(data);
            })
            .catch((error) => {
              hideLoading();
              showError("Error sending emails: " + error.message);
            });
        }
      });
    }

    function showResults(data) {
      const resultsContent = document.getElementById("resultsContent");

      if (data.success) {
        // Clean results display without test mode references
        const results = data.results || {};
        const errorsCount = results.errors_count || 0;
        const hasErrors = results.errors && results.errors.length > 0;

        resultsContent.innerHTML = `
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h3 class="text-lg font-medium text-green-800 mb-2">
                        <i class="fas fa-check-circle mr-2"></i>RFQ Email Sent Successfully
                    </h3>
                    <div class="text-sm text-green-700">
                        <p><strong>Status:</strong> Email delivered successfully</p>
                        <p><strong>Recipients:</strong> XFab team notified</p>
                        ${errorsCount > 0 ? `<p><strong>Issues:</strong> ${errorsCount} warnings</p>` : ''}
                    </div>
                    ${
                      hasErrors
                        ? `
                        <div class="mt-3">
                            <p class="font-medium text-red-700">Warnings:</p>
                            <ul class="text-sm text-red-600 list-disc list-inside">
                                ${results.errors
                                  .map((error) => `<li>${error}</li>`)
                                  .join("")}
                            </ul>
                        </div>
                    `
                        : ""
                    }
                </div>
            `;
      } else {
        resultsContent.innerHTML = `
                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <h3 class="text-lg font-medium text-red-800 mb-2">
                        <i class="fas fa-exclamation-circle mr-2"></i>Email Sending Failed
                    </h3>
                    <p class="text-sm text-red-700">${data.message}</p>
                </div>
            `;
      }

      resultsSection.classList.remove("hidden");
    }



    function showLoading(text) {
      loadingText.textContent = text;
      loadingModal.classList.remove("hidden");
    }

    function hideLoading() {
      loadingModal.classList.add("hidden");
    }

    function showSuccess(message) {
      Swal.fire({
        icon: "success",
        title: "Success!",
        text: message,
        timer: 3000,
        showConfirmButton: false,
        toast: true,
        position: "top-end",
      });
    }

    function showError(message) {
      Swal.fire({
        icon: "error",
        title: "Error!",
        text: message,
        confirmButtonText: "OK",
        confirmButtonColor: "#dc3545",
      });
    }



    function clearSession() {
      Swal.fire({
        title: "Clear Form Data?",
        text: "This will clear all form fields and reset the RFQ form.",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#dc3545",
        cancelButtonColor: "#6c757d",
        confirmButtonText: "Yes, Clear Form!",
        cancelButtonText: "Cancel",
      }).then((result) => {
        if (result.isConfirmed) {
          // Clear form and reset state
          clearForm();
          resultsSection.classList.add("hidden");
          sendSection.classList.add("hidden");
          showSuccess("Form cleared! You can now start fresh.");
        }
      });
    }
  });

