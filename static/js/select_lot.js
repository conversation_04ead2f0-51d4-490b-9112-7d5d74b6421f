/**
 * select_lot.js
 * Handles the logic for the Label & Packing Slip Generation interface
 */

document.addEventListener("DOMContentLoaded", function () {
  // Initialize all functionality on page load
  initializeAll();
});

/**
 * Initialize all components and functionality
 */
function initializeAll() {
  initializeTabs();
  initializeFormSubmission();
  initializeTheme();
  initializeFormHandlers();
  initializeButtons();
  initializeAnimations();
}

/**
 * Initialize tab functionality with smooth transitions and indicator
 */
function initializeTabs() {
  const tabs = document.querySelectorAll(".tab-button");
  const contents = document.querySelectorAll(".tab-content");
  const indicator = document.querySelector(".tab-indicator");
  let activeTab = "asana"; // Default active tab

  // Set initial indicator position
  if (indicator) {
    const activeButton = document.querySelector(".tab-button.active");
    if (activeButton) {
      updateIndicator(activeButton);
    }
  }

  tabs.forEach((tab) => {
    tab.addEventListener("click", () => {
      // Remove active state from all tabs
      tabs.forEach((t) => t.classList.remove("active"));

      // Add active state to clicked tab
      tab.classList.add("active");

      // Update indicator position
      if (indicator) {
        updateIndicator(tab);
      }

      // Update content visibility with animation
      const targetId = `${tab.dataset.tab}-tab`;
      contents.forEach((content) => {
        if (content.id === targetId) {
          content.classList.remove("hidden");
          // Trigger reflow for animation
          void content.offsetWidth;
          content.classList.add("active");
        } else {
          content.classList.remove("active");
          // Add hidden class after transition
          setTimeout(() => {
            if (!content.classList.contains("active")) {
              content.classList.add("hidden");
            }
          }, 300);
        }
      });

      activeTab = tab.dataset.tab;

      // Initialize Smart Sync Dashboard when switching to icarium-sync tab
      if (activeTab === 'icarium-sync') {
        initializeSmartSyncDashboard();
      }

      // Clear form fields when switching tabs
      clearFields();
    });
  });
}

/**
 * Updates the position and width of the tab indicator
 * @param {HTMLElement} tab - The active tab element
 */
function updateIndicator(tab) {
  const indicator = document.querySelector(".tab-indicator");
  if (!indicator) return;

  indicator.style.width = `${tab.offsetWidth}px`;
  indicator.style.left = `${tab.offsetLeft}px`;
}

/**
 * Initialize form submission with validation and loading states
 */
function initializeFormSubmission() {
  const form = document.querySelector("form");
  const lotSelect = document.querySelector('select[name="lot"]');
  const multipleLotsInput = document.querySelector(
    'textarea[name="multiple_lots"]'
  );
  const asanaLinkInput = document.getElementById("asana_link");

  // Field clearing logic
  lotSelect?.addEventListener("change", function () {
    if (this.value) {
      multipleLotsInput.value = "";
      asanaLinkInput.value = "";
    }
  });

  multipleLotsInput?.addEventListener("input", function () {
    if (this.value) {
      lotSelect.value = "";
      asanaLinkInput.value = "";
    }
  });

  asanaLinkInput?.addEventListener("input", function () {
    if (this.value) {
      lotSelect.value = "";
      multipleLotsInput.value = "";
    }
  });

  // Form submission with enhanced AJAX handling
  form?.addEventListener("submit", function (e) {
    console.log("Form submission triggered");
    e.preventDefault();

    // Get active tab
    const activeTabContent = document.querySelector(".tab-content.active");
    const activeTabId = activeTabContent?.id?.replace("-tab", "");

    // Validation
    let isValid = false;
    let errorMessage = "";

    if (activeTabId === "manual") {
      const lotValue = lotSelect?.value;
      const multipleLotsValue = multipleLotsInput?.value.trim();

      isValid = lotValue || multipleLotsValue;
      errorMessage = "Please select a lot or enter multiple lots";
    } else if (activeTabId === "asana") {
      isValid = asanaLinkInput?.value.trim();
      errorMessage = "Please enter an Asana task link";
    } else if (activeTabId === "search") {
      // Validate search criteria
      const searchLot = document.getElementById("search_lot")?.value.trim();
      const searchScribe = document
        .getElementById("search_scribe")
        ?.value.trim();
      isValid = searchLot || searchScribe;
      errorMessage = "Please enter at least one search criteria";
    } else if (activeTabId === "freestyle") {
      // Validate free style inputs
      const itemId = document.getElementById("fs_item_id")?.value.trim();
      const waferCount = document
        .getElementById("fs_wafer_count")
        ?.value.trim();
      const svmLotId = document.getElementById("fs_svm_lot_id")?.value.trim();

      isValid = itemId && waferCount && svmLotId;

      if (!itemId) {
        errorMessage = "Please enter an Item ID";
      } else if (!waferCount) {
        errorMessage = "Please enter a Wafer Count";
      } else if (!svmLotId) {
        errorMessage = "Please enter an SVM Lot ID";
      }
    }

    if (!isValid) {
      Swal.fire({
        icon: "error",
        title: "Selection Required",
        text: errorMessage,
        showClass: {
          popup: "animate__animated animate__fadeIn animate__faster",
        },
        hideClass: {
          popup: "animate__animated animate__fadeOut animate__faster",
        },
      });
      return;
    }

    // Initialize progress indicator based on active tab
    let progressSteps = [];
    if (activeTabId === "asana") {
      progressSteps = [
        'Validating Asana link',
        'Fetching task information',
        'Processing wafer data',
        'Redirecting to results'
      ];
    } else if (activeTabId === "search") {
      progressSteps = [
        'Validating search criteria',
        'Querying database',
        'Processing results',
        'Displaying matches'
      ];
    } else if (activeTabId === "freestyle") {
      progressSteps = [
        'Validating input data',
        'Generating label',
        'Creating PDF',
        'Preparing download'
      ];
    } else {
      progressSteps = [
        'Validating selection',
        'Processing lots',
        'Preparing data',
        'Redirecting'
      ];
    }

    // Show progress indicator
    const progress = window.progressIndicator || new ProgressIndicator();
    progress.init('Processing Request', progressSteps);

    // Special handling for Asana tab to use AJAX
    if (activeTabId === "asana") {
      submitAsanaForm(this, new FormData(this));
    } else if (activeTabId === "search") {
      // Handle Quick Search tab with AJAX
      submitQuickSearchForm(this, new FormData(this));
    } else if (activeTabId === "freestyle") {
      // Handle Free Style tab with AJAX
      submitFreeStyleForm(this, new FormData(this));
    } else {
      // Standard form submission for other tabs
      this.submit();
    }
  });
}

/**
 * Submit Free Style form via AJAX
 * @param {HTMLFormElement} form - The form element
 * @param {FormData} formData - The form data to submit
 */
async function submitFreeStyleForm(form, formData) {
  try {
    // Show loading indicator
    Swal.fire({
      title: "Processing...",
      html: `
        <div class="flex flex-col items-center">
          <div class="loading-spinner mb-4"></div>
          <p>Please wait while we generate your Substrate-wafer label...</p>
        </div>
      `,
      showConfirmButton: false,
      allowOutsideClick: false,
      customClass: {
        popup: "rounded-xl",
      },
      didOpen: () => {
        const style = document.createElement("style");
        style.textContent = `
          .loading-spinner {
            width: 48px;
            height: 48px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #4f46e5;
            border-radius: 50%;
            animation: spin 1s linear infinite;
          }
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `;
        Swal.getPopup().appendChild(style);
      },
    });

    // Add a flag to indicate this is a free style submission
    formData.append("is_freestyle", "true");

    // Send the free style request
    const baseUrl = form.action.split("/").slice(0, -1).join("/");
    const freestyleUrl = `${baseUrl}/freestyle`;
    console.log("Submitting free style to URL:", freestyleUrl);

    // Try to get CSRF token
    let csrfToken = "";
    try {
      csrfToken = getCsrfToken();
    } catch (e) {
      console.warn("CSRF token not found, continuing without it:", e);
    }

    const response = await fetch(freestyleUrl, {
      method: "POST",
      body: formData,
      headers: {
        "X-Requested-With": "XMLHttpRequest",
        "X-CSRFToken": csrfToken,
      },
    });

    // Check if JSON response
    const contentType = response.headers.get("content-type");
    if (contentType && contentType.includes("application/json")) {
      const data = await response.json();

      if (response.ok) {
        if (data.redirect) {
          // Update the loading message to indicate we're redirecting to download page
          Swal.update({
            html: `
              <div class="flex flex-col items-center">
                <div class="loading-spinner mb-4"></div>
                <p>Label generated successfully!</p>
                <p class="text-sm text-gray-500 mt-2">Redirecting to download page...</p>
              </div>
            `,
          });

          // Short delay to show the success message before redirecting
          setTimeout(() => {
            window.location.href = data.redirect;
          }, 1000);
        } else if (data.message) {
          Swal.fire({
            icon: "info",
            title: "Substrate-wafer Label",
            text: data.message,
            confirmButtonColor: "#4f46e5",
          });
        } else {
          throw new Error("No redirect URL or message provided in response");
        }
      } else {
        throw new Error(
          data.message || `HTTP error! status: ${response.status}`
        );
      }
    } else {
      // Handle HTML response (traditional redirect)
      if (response.ok) {
        window.location.href = response.url;
      } else {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    }
  } catch (error) {
    console.error("Error:", error);
    Swal.fire({
      icon: "error",
      title: "Substrate-wafer Label Failed",
      text:
        error.message ||
        "An error occurred while processing your Substrate-wafer label. Please try again.",
      confirmButtonColor: "#4f46e5",
    });
  }
}

/**
 * Submit Quick Search form via AJAX
 * @param {HTMLFormElement} form - The form element
 * @param {FormData} formData - The form data to submit
 */
async function submitQuickSearchForm(form, formData) {
  try {
    // Show loading indicator
    Swal.fire({
      title: "Searching...",
      html: `
        <div class="flex flex-col items-center">
          <div class="loading-spinner mb-4"></div>
          <p>Please wait while we search for matching wafers...</p>
        </div>
      `,
      showConfirmButton: false,
      allowOutsideClick: false,
      customClass: {
        popup: "rounded-xl",
      },
      didOpen: () => {
        const style = document.createElement("style");
        style.textContent = `
          .loading-spinner {
            width: 48px;
            height: 48px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #4f46e5;
            border-radius: 50%;
            animation: spin 1s linear infinite;
          }
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `;
        Swal.getPopup().appendChild(style);
      },
    });

    // Add a flag to indicate this is a quick search
    formData.append("is_quick_search", "true");

    // Send the search request
    // Use the form's action URL as the base and append the quick_search endpoint
    const baseUrl = form.action.split("/").slice(0, -1).join("/");
    const searchUrl = `${baseUrl}/quick_search`;
    console.log("Submitting quick search to URL:", searchUrl);

    const response = await fetch(searchUrl, {
      method: "POST",
      body: formData,
      headers: {
        "X-Requested-With": "XMLHttpRequest",
      },
    });

    // Check if JSON response
    const contentType = response.headers.get("content-type");
    if (contentType && contentType.includes("application/json")) {
      const data = await response.json();

      if (response.ok) {
        if (data.redirect) {
          window.location.href = data.redirect;
        } else if (data.message) {
          Swal.fire({
            icon: "info",
            title: "Search Results",
            text: data.message,
            confirmButtonColor: "#4f46e5",
          });
        } else {
          throw new Error("No redirect URL or message provided in response");
        }
      } else {
        throw new Error(
          data.message || `HTTP error! status: ${response.status}`
        );
      }
    } else {
      // Handle HTML response (traditional redirect)
      if (response.ok) {
        window.location.href = response.url;
      } else {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    }
  } catch (error) {
    console.error("Error:", error);
    Swal.fire({
      icon: "error",
      title: "Search Failed",
      text:
        error.message ||
        "An error occurred while processing your search. Please try again.",
      confirmButtonColor: "#4f46e5",
    });
  }
}

/**
 * Submit Asana form via AJAX
 * @param {HTMLFormElement} form - The form element
 * @param {FormData} formData - The form data to submit
 */
async function submitAsanaForm(form, formData) {
  const progress = window.progressIndicator || new ProgressIndicator();

  try {
    // Step 1: Validate Asana link
    progress.updateProgress(0);

    // Step 2: Fetch task information
    progress.nextStep('Fetching task information...');

    const response = await fetch(form.action, {
      method: "POST",
      body: formData,
      headers: {
        "X-Requested-With": "XMLHttpRequest",
      },
    });

    // Step 3: Process wafer data
    progress.nextStep('Processing wafer data...');

    // Check if JSON response
    const contentType = response.headers.get("content-type");
    if (contentType && contentType.includes("application/json")) {
      const data = await response.json();

      if (response.ok) {
        if (data.redirect) {
          // Step 4: Redirect
          progress.nextStep('Redirecting to results...');
          progress.complete('Task processed successfully!');

          setTimeout(() => {
            window.location.href = data.redirect;
          }, 1000);
        } else {
          throw new Error("No redirect URL provided in response");
        }
      } else {
        throw new Error(
          data.message || `HTTP error! status: ${response.status}`
        );
      }
    } else {
      // Handle HTML response (traditional redirect)
      if (response.ok) {
        progress.complete('Processing complete!');
        setTimeout(() => {
          window.location.href = response.url;
        }, 1000);
      } else {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    }
  } catch (error) {
    console.error("Error:", error);
    progress.error(`Processing failed: ${error.message}`);

    Swal.fire({
      icon: "error",
      title: "Request Failed",
      text:
        error.message ||
        "An error occurred while processing your request. Please try again.",
      confirmButtonColor: "#4f46e5",
    });
  }
}

/**
 * Initialize theme toggling functionality
 */
function initializeTheme() {
  const themeToggle = document.getElementById("themeToggle");
  const html = document.documentElement;
  const savedTheme = localStorage.getItem("theme") || "light";

  function setTheme(theme) {
    if (theme === "dark") {
      html.classList.add("dark");
      document.querySelector(".fa-sun")?.classList.add("hidden");
      document.querySelector(".fa-moon")?.classList.remove("hidden");
    } else {
      html.classList.remove("dark");
      document.querySelector(".fa-sun")?.classList.remove("hidden");
      document.querySelector(".fa-moon")?.classList.add("hidden");
    }
    localStorage.setItem("theme", theme);
  }

  setTheme(savedTheme);

  if (themeToggle) {
    themeToggle.addEventListener("click", () => {
      const currentTheme = html.classList.contains("dark") ? "light" : "dark";
      setTheme(currentTheme);
    });
  }
}

/**
 * Initialize form input handlers and validations
 */
function initializeFormHandlers() {
  // Initialize date validation
  const dateFrom = document.getElementById("search_date_from");
  const dateTo = document.getElementById("search_date_to");

  if (dateFrom && dateTo) {
    dateFrom.addEventListener("change", validateDates);
    dateTo.addEventListener("change", validateDates);
  }

  // Remove required attribute from date inputs to prevent form validation issues
  document.querySelectorAll('input[type="date"]').forEach((input) => {
    input.removeAttribute("required");
  });
}

/**
 * Initialize action buttons
 */
function initializeButtons() {
  // Initialize Eiger CSV button
  const eigerCsvBtn = document.getElementById("eigerCsvBtn");
  if (eigerCsvBtn) {
    eigerCsvBtn.addEventListener("click", generateEigerCsv);
  }

  // Initialize email buttons
  const sendEmailBtn = document.getElementById("sendEmailBtn");
  if (sendEmailBtn) {
    sendEmailBtn.addEventListener("click", showEmailPreview);
  }
}

/**
 * Initialize UI animations and interactions
 */
function initializeAnimations() {
  // Add entrance animations for page elements
  const header = document.querySelector(".container > div:first-child");
  const mainCard = document.querySelector(".card-hover");

  if (header && mainCard) {
    // Apply subtle entrance animations
    header.style.opacity = "0";
    header.style.transform = "translateY(-20px)";

    mainCard.style.opacity = "0";
    mainCard.style.transform = "translateY(20px)";

    // Trigger animations after a small delay
    setTimeout(() => {
      header.style.transition = "opacity 600ms ease, transform 600ms ease";
      header.style.opacity = "1";
      header.style.transform = "translateY(0)";

      setTimeout(() => {
        mainCard.style.transition = "opacity 600ms ease, transform 600ms ease";
        mainCard.style.opacity = "1";
        mainCard.style.transform = "translateY(0)";
      }, 200);
    }, 100);
  }
}

/**
 * Clear all form fields
 */
function clearFields() {
  const fields = [
    "lot",
    "multiple_lots",
    "asana_link",
    "search_lot",
    "search_scribe",
    "search_date_from",
    "search_date_to",
    "fs_item_id",
    "fs_wafer_count",
    "fs_svm_lot_id",
    "fs_comments",
  ];

  fields.forEach((id) => {
    const element = document.getElementById(id);
    if (element) {
      element.value = "";
    }
  });
}

/**
 * Validate date range inputs
 */
function validateDates() {
  const dateFrom = document.getElementById("search_date_from");
  const dateTo = document.getElementById("search_date_to");

  if (dateFrom && dateTo && dateFrom.value && dateTo.value) {
    if (dateFrom.value > dateTo.value) {
      dateTo.setCustomValidity("End date must be after start date");
      dateTo.reportValidity();

      // Visual feedback
      dateTo.classList.add("border-red-500");

      // Remove error styling after correction
      dateTo.addEventListener("input", function removeError() {
        if (dateFrom.value <= dateTo.value) {
          dateTo.classList.remove("border-red-500");
          dateTo.setCustomValidity("");
          dateTo.removeEventListener("input", removeError);
        }
      });
    } else {
      dateTo.setCustomValidity("");
      dateTo.classList.remove("border-red-500");
    }
  }
}

/**
 * Get CSRF token from the page
 * @returns {string} CSRF token
 */
function getCsrfToken() {
  const token =
    document.querySelector('meta[name="csrf-token"]')?.content ||
    document.querySelector('input[name="csrf_token"]')?.value ||
    document.querySelector('[name="csrf_token"]')?.value;

  if (!token) {
    throw new Error("CSRF token not found");
  }
  return token;
}

/**
 * Generate Eiger CSV file
 */
async function generateEigerCsv() {
  try {
    const csrfToken = getCsrfToken();

    const { value: asanaLink, isConfirmed } = await Swal.fire({
      title: "Generate Eiger CSV",
      html: `
        <div class="text-left">
          <label class="block text-sm font-medium text-gray-700 mb-2">Asana Task Link</label>
          <input id="swal-input-asana" class="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500" placeholder="Paste your Asana task link here">
        </div>
      `,
      showCancelButton: true,
      confirmButtonText: "Generate",
      confirmButtonColor: "#10b981",
      cancelButtonColor: "#6b7280",
      preConfirm: () => {
        const input = document.getElementById("swal-input-asana");
        if (!input.value.trim()) {
          Swal.showValidationMessage("Please enter an Asana task link");
          return false;
        }
        return input.value;
      },
      customClass: {
        popup: "rounded-lg",
      },
    });

    if (!isConfirmed || !asanaLink) return;

    Swal.fire({
      title: "Generating CSV",
      html: `
        <div class="flex flex-col items-center">
          <div class="csv-loading-spinner mb-4"></div>
          <div class="text-sm text-gray-500">Fetching task information and generating CSV file...</div>
        </div>
      `,
      customClass: {
        popup: "rounded-lg",
      },
      showConfirmButton: false,
      allowOutsideClick: false,
      didOpen: () => {
        const style = document.createElement("style");
        style.textContent = `
          .csv-loading-spinner {
            width: 48px;
            height: 48px;
            border: 5px solid #d1fae5;
            border-top: 5px solid #10b981;
            border-radius: 50%;
            animation: spin 1s linear infinite;
          }
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `;
        Swal.getPopup().appendChild(style);
      },
    });

    // Add a small delay to show the loading animation
    await new Promise((resolve) => setTimeout(resolve, 800));

    // First, get the task info to extract the task name
    // Extract the task GID from various Asana URL formats
    const taskGidMatch =
      asanaLink.match(/\/task\/(\d+)/) ||
      asanaLink.match(/\/(\d+)\?/) ||
      asanaLink.match(/\/(\d+)$/) ||
      asanaLink.match(/\/project\/\d+\/task\/(\d+)/);
    const taskGid = taskGidMatch ? taskGidMatch[1] : null;

    if (!taskGid) {
      throw new Error("Invalid Asana task link format");
    }

    // Fetch task info to get the task name
    const taskInfoResponse = await fetch(
      `/api/get_task_info?task_gid=${taskGid}`
    );
    let taskName = "";

    if (taskInfoResponse.ok) {
      const taskInfoData = await taskInfoResponse.json();
      if (
        taskInfoData.success &&
        taskInfoData.task_info &&
        taskInfoData.task_info.name
      ) {
        // Get the original task name
        const originalName = taskInfoData.task_info.name;

        // Extract project info using similar logic to the backend
        let projectInfo = "";

        // Try to extract relevant project information and exclude lot IDs and "label free" mentions
        // Look for patterns like "FAU WP1.9 RI_Oxide" or parts after commas
        const projectMatch = originalName.match(
          /([A-Z]+\s*W?P?\.?\d+\.?\d*\s*[A-Z][A-Za-z0-9_]+(?:\s*[-]\s*\d+\s*[A-Z]+\s*)*)/
        );

        if (projectMatch) {
          projectInfo = projectMatch[1].trim();
        } else {
          // Try to find any part after a comma that might contain the project info
          const commaMatch = originalName.match(/,\s*([^,]+)(?:,|$)/);
          if (commaMatch) {
            projectInfo = commaMatch[1].trim();
          } else {
            // Just use the name but try to remove "Eiger" and "Label free" parts
            projectInfo = originalName
              .replace(/Eiger\s*Label\s*free\s*:\s*[^,]+,/i, "")
              .replace(/^Eiger\s*/i, "")
              .trim();
          }
        }

        // Clean the project info for use in a filename
        taskName = projectInfo
          .replace(/[^a-zA-Z0-9]/g, "_") // Replace special chars with underscore
          .replace(/_+/g, "_") // Replace multiple underscores with a single one
          .replace(/^_|_$/g, "") // Remove leading/trailing underscores
          .substring(0, 50); // Limit length to avoid very long filenames
      }
    }

    const response = await fetch("/generate_eiger_csv", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CSRFToken": csrfToken,
      },
      body: JSON.stringify({
        asana_link: asanaLink,
        direct_generation: true,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Failed to generate CSV");
    }

    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;

    // Get the filename from the Content-Disposition header if available
    let serverFilename = "";

    // Extract the filename from the Content-Disposition header
    const contentDisposition = response.headers.get("Content-Disposition");
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
      if (filenameMatch && filenameMatch[1]) {
        serverFilename = filenameMatch[1];
        console.log("Using server-provided filename:", serverFilename);
      }
    }

    // If we got a server filename, use it
    if (serverFilename) {
      a.download = serverFilename;
    } else {
      // Fall back to client-side filename generation
      console.log("No server filename found, generating locally");

      // Create a timestamp with date and time (YYYY-MM-DD_HH-MM-SS)
      const now = new Date();
      const dateStr = now.toISOString().slice(0, 10);
      const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, "-");
      const timestamp = `${dateStr}_${timeStr}`;

      // Check if taskName already contains "Eiger" to avoid duplication
      if (taskName.toLowerCase().startsWith("eiger_")) {
        taskName = taskName.substring(6); // Remove "Eiger_" prefix
      } else if (taskName.toLowerCase().startsWith("eiger")) {
        taskName = taskName.substring(5); // Remove "Eiger" prefix
      }

      // Always prepend "Eiger_" to the filename
      a.download = taskName
        ? `Eiger_${taskName}_${timestamp}.csv`
        : `Eiger_${timestamp}.csv`;
    }

    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);

    await Swal.fire({
      icon: "success",
      title: "Success",
      text: "CSV file has been generated and downloaded.",
      confirmButtonText: "Done",
      confirmButtonColor: "#10b981",
      customClass: {
        popup: "rounded-lg",
      },
    });
  } catch (error) {
    console.error("CSV generation error:", error);
    await Swal.fire({
      icon: "error",
      title: "Error",
      text:
        error.message ||
        "Failed to generate CSV file. Please check the Asana link and try again.",
      confirmButtonText: "OK",
      confirmButtonColor: "#4f46e5",
      customClass: {
        popup: "rounded-lg",
      },
    });
  }
}
/**
 * Send notification email
 */
async function sendNotification() {
  try {
    const csrfToken = getCsrfToken();

    // Prompt for Asana task link
    const { value: asanaLink, isConfirmed } = await Swal.fire({
      title: "Send Email Notification",
      html: `
        <div class="text-left">
          <label class="block text-sm font-medium text-gray-700 mb-2">Asana Task Link</label>
          <input id="swal-input-asana-email" class="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500" placeholder="Paste your Asana task link here">
        </div>
      `,
      showCancelButton: true,
      confirmButtonText: "Send Email",
      confirmButtonColor: "#3b82f6",
      cancelButtonColor: "#6b7280",
      preConfirm: () => {
        const input = document.getElementById("swal-input-asana-email");
        if (!input.value.trim()) {
          Swal.showValidationMessage("Please enter an Asana task link");
          return false;
        }
        return input.value;
      },
      customClass: {
        popup: "rounded-lg",
      },
    });

    if (!isConfirmed || !asanaLink) return;

    // Extract the task GID from various Asana URL formats
    const taskGidMatch =
      asanaLink.match(/\/task\/(\d+)/) ||
      asanaLink.match(/\/(\d+)\?/) ||
      asanaLink.match(/\/(\d+)$/) ||
      asanaLink.match(/\/project\/\d+\/task\/(\d+)/);
    const taskGid = taskGidMatch ? taskGidMatch[1] : null;

    if (!taskGid) {
      throw new Error("Invalid Asana task link. Please provide a valid link.");
    }

    // First, check if this is an Eiger project by making a preliminary call
    const checkResponse = await fetch(`/api/get_task_info?task_gid=${taskGid}`);
    const taskData = await checkResponse.json();

    if (!taskData.success) {
      throw new Error(taskData.message || "Failed to get task information");
    }

    // Check if it's an Eiger project by looking at Lot project field
    const lotProject = taskData.task_info["Lot project"] || "";
    const isEigerProject = lotProject.toLowerCase().includes("eiger");

    // If not an Eiger project, show a confirmation dialog first
    if (!isEigerProject) {
      const { isConfirmed } = await Swal.fire({
        icon: "warning",
        title: "Non-Eiger Shipment",
        html: `
          <div class="text-left">
            <p class="font-semibold mb-2">This does not appear to be an Eiger shipment.</p>
            <p class="mb-4">The Lot project field does not contain 'Eiger'.</p>
            <p>Do you want to send the email notification anyway?</p>
          </div>
        `,
        showCancelButton: true,
        confirmButtonText: "Yes, Send Anyway",
        cancelButtonText: "Cancel",
        confirmButtonColor: "#f59e0b",
        cancelButtonColor: "#6b7280",
        customClass: {
          popup: "rounded-lg",
        },
      });

      if (!isConfirmed) {
        return; // User canceled the operation
      }
    }

    Swal.fire({
      title: "Sending Notification",
      html: `
        <div class="flex flex-col items-center">
          <div class="email-loading-spinner mb-4"></div>
          <div class="text-sm text-gray-500">Preparing and sending email notification...</div>
        </div>
      `,
      showConfirmButton: false,
      allowOutsideClick: false,
      allowEscapeKey: false,
      customClass: {
        popup: "rounded-lg",
      },
      didOpen: () => {
        const style = document.createElement("style");
        style.textContent = `
          .email-loading-spinner {
            width: 48px;
            height: 48px;
            border: 5px solid #dbeafe;
            border-top: 5px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
          }
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `;
        Swal.getPopup().appendChild(style);
      },
    });

    // Add a small delay to show the loading animation
    await new Promise((resolve) => setTimeout(resolve, 800));

    const response = await fetch("/api/send_notification", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CSRFToken": csrfToken,
      },
      body: JSON.stringify({
        task_gid: taskGid,
        type: "shipping",
      }),
    });

    const data = await response.json();

    if (data.success) {
      // Check if it's not an Eiger project but sent anyway
      if (data.warning && !data.is_eiger) {
        await Swal.fire({
          icon: "warning",
          title: "Non-Eiger Shipment",
          html: `
            <div class="text-left">
              <p class="mb-4">Warning: ${data.warning}</p>
              <p>The email notification was sent successfully, but please verify this was intentional.</p>
            </div>
          `,
          confirmButtonColor: "#f59e0b",
          customClass: {
            popup: "rounded-lg",
          },
        });
      } else {
        await Swal.fire({
          icon: "success",
          title: "Success",
          text: "Email notification sent successfully!",
          confirmButtonColor: "#3b82f6",
          customClass: {
            popup: "rounded-lg",
          },
        });
      }
    } else if (data.message && data.message.includes("tracking number")) {
      await Swal.fire({
        icon: "warning",
        title: "Tracking Number Required",
        text: "Please ensure the UPS Tracking Number is filled in Asana before sending the notification.",
        confirmButtonColor: "#f59e0b",
        customClass: {
          popup: "rounded-lg",
        },
      });
    } else {
      throw new Error(data.message || "Failed to send notification");
    }
  } catch (error) {
    console.error("Email error:", error);
    await Swal.fire({
      icon: "error",
      title: "Error",
      html: `Failed to send notification:<br>${error.message}`,
      confirmButtonColor: "#ef4444",
      customClass: {
        popup: "rounded-lg",
      },
    });
  }
}

/**
 * Get task GID from various sources
 * @returns {string} Asana task GID
 */
async function getTaskGid() {
  const taskGid =
    document.querySelector('[name="task_gid"]')?.value ||
    document.querySelector("#asana_link")?.value?.match(/\d+$/)?.[0] ||
    (typeof sessionStorage !== "undefined" &&
      sessionStorage.getItem("current_task_gid"));

  if (!taskGid) {
    throw new Error(
      "No task selected. Please select a task or enter an Asana link first."
    );
  }
  return taskGid;
}



/**
 * Initialize Smart Sync Dashboard when the tab is activated
 */
function initializeSmartSyncDashboard() {
  console.log('🧠 Initializing Smart Sync Dashboard');

  // Check if SmartSyncDashboard class is available
  if (typeof SmartSyncDashboard !== 'undefined') {
    // Initialize the dashboard if not already done
    if (!window.smartSyncDashboard) {
      window.smartSyncDashboard = new SmartSyncDashboard('smart-sync-dashboard');
    } else {
      // Refresh data if dashboard already exists
      window.smartSyncDashboard.loadRecommendations();
    }
  } else {
    console.warn('⚠️ SmartSyncDashboard class not found. Make sure smart-sync-dashboard.js is loaded.');

    // Show a fallback message
    const container = document.getElementById('smart-sync-dashboard');
    if (container) {
      container.innerHTML = `
        <div class="text-center py-12">
          <div class="mb-4">
            <i class="fas fa-exclamation-triangle text-yellow-500 text-4xl"></i>
          </div>
          <h3 class="text-lg font-semibold text-gray-700 mb-2">Smart Sync Dashboard Loading...</h3>
          <p class="text-gray-500 mb-4">The Smart Sync features are being loaded. Please refresh the page if this message persists.</p>
          <button onclick="location.reload()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
            <i class="fas fa-refresh mr-2"></i>
            Refresh Page
          </button>
        </div>
      `;
    }
  }
}

/**
 * Show email preview modal
 */
async function showEmailPreview() {
  try {
    // Get task GID from the Asana link input field
    const asanaLinkInput = document.getElementById("asana_link");
    let taskGid = null;

    if (asanaLinkInput && asanaLinkInput.value) {
      const asanaLink = asanaLinkInput.value.trim();
      // Extract task GID from Asana URL
      const taskGidMatch =
        asanaLink.match(/\/task\/(\d+)/) ||
        asanaLink.match(/\/(\d+)\?/) ||
        asanaLink.match(/\/(\d+)$/) ||
        asanaLink.match(/\/project\/\d+\/task\/(\d+)/);
      taskGid = taskGidMatch ? taskGidMatch[1] : null;

      // Check if asanaLink is just a number (direct GID input)
      if (!taskGid && /^\d+$/.test(asanaLink)) {
        taskGid = asanaLink;
      }
    }

    // Also check sessionStorage as fallback
    if (!taskGid) {
      taskGid = sessionStorage.getItem("current_task_gid");
    }

    if (!taskGid) {
      await Swal.fire({
        icon: "error",
        title: "No Asana Task Found",
        text: "Please enter an Asana task link in the 'Asana Task Link' field before sending email.",
        customClass: {
          popup: "rounded-lg",
        },
      });
      return;
    }

    // Show loading
    Swal.fire({
      title: "Generating email preview...",
      text: "Please wait...",
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
      },
    });

    // Generate preview
    const response = await fetch("/api/send_notification", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CSRFToken": getCsrfToken(),
      },
      body: JSON.stringify({
        task_gid: taskGid,
        preview_mode: true,
      }),
    });

    const data = await response.json();
    Swal.close();

    if (data.success && data.preview) {
      // Show preview modal
      showPreviewModal(data.preview, taskGid, data.warning, data.is_eiger);
    } else {
      throw new Error(data.message || "Failed to generate preview");
    }
  } catch (error) {
    Swal.close();
    console.error("Preview error:", error);
    Swal.fire({
      icon: "error",
      title: "Error",
      text: error.message || "Failed to generate email preview",
      customClass: {
        popup: "rounded-lg",
      },
    });
  }
}

function showPreviewModal(previewData, taskGid, warning, isEiger) {
  // Create modal HTML (same as download_files.js)
  const modalHTML = `
    <div id="emailPreviewModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[95vh] flex flex-col">
        <!-- Modal Header -->
        <div class="bg-gradient-to-r from-indigo-600 to-indigo-700 text-white p-6 flex-shrink-0 rounded-t-lg">
          <div class="flex justify-between items-center">
            <h3 class="text-2xl font-bold">
              <i class="fas fa-eye mr-3"></i>
              Eiger Email Preview & Editor
            </h3>
            <button id="closePreviewModal" class="text-white hover:text-indigo-200 transition-colors">
              <i class="fas fa-times text-2xl"></i>
            </button>
          </div>
          <p class="text-indigo-100 mt-2 text-sm">
            Review and customize your Eiger shipping notification before sending
          </p>
          ${warning ? `
            <div class="mt-3 p-3 bg-yellow-500 bg-opacity-20 border border-yellow-400 rounded-md">
              <div class="flex items-center">
                <i class="fas fa-exclamation-triangle text-yellow-300 mr-2"></i>
                <span class="text-yellow-100 text-sm">${warning}</span>
              </div>
            </div>
          ` : ''}
        </div>

        <!-- Modal Body -->
        <div class="p-6 overflow-y-auto flex-1 min-h-0">
          <!-- Email Details -->
          <div class="space-y-6 mb-8">
            <!-- Subject Line -->
            <div class="form-group">
              <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                <i class="fas fa-edit mr-2 text-blue-600"></i>Subject Line (Editable)
              </label>
              <input type="text" id="previewSubject" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                     value="${previewData.subject}" />
            </div>

            <!-- Recipients -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="form-group">
                <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                  <i class="fas fa-users mr-2 text-green-600"></i>To Recipients (Editable)
                </label>
                <input type="text" id="previewTo" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                       value="${previewData.recipients.to.join(', ')}" />
              </div>
              <div class="form-group">
                <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                  <i class="fas fa-user-plus mr-2 text-purple-600"></i>CC Recipients (Editable)
                </label>
                <input type="text" id="previewCC" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                       value="${previewData.recipients.cc.join(', ')}" />
              </div>
            </div>
          </div>

          <!-- Email Body Editor -->
          <div class="form-group mb-6">
            <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
              <i class="fas fa-edit mr-2 text-blue-600"></i>Email Body (Fully Editable)
            </label>
            <div class="border-2 border-gray-200 dark:border-gray-600 rounded-xl overflow-hidden">
              <textarea id="emailBodyEditor" class="w-full min-h-[300px] p-6 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                        style="line-height: 1.8; font-size: 15px; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">${previewData.body}</textarea>
            </div>
            <div class="mt-4 p-4 bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg">
              <div class="flex items-start">
                <i class="fas fa-magic text-blue-600 dark:text-blue-400 mr-3 mt-1"></i>
                <div>
                  <h5 class="font-semibold text-blue-800 dark:text-blue-200 mb-1">
                    Maximum Editing Flexibility
                  </h5>
                  <p class="text-sm text-blue-700 dark:text-blue-300">
                    Edit any part of the email including subject, recipients, body content, tracking information, and signature. All changes will be reflected in the final email.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Modal Footer -->
        <div class="border-t border-gray-200 dark:border-gray-600 p-6 bg-gray-50 dark:bg-gray-800 flex-shrink-0 rounded-b-lg">
          <div class="flex flex-col sm:flex-row gap-3">
            <button type="button" id="refreshPreviewBtn" class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 transition-colors text-sm">
              <i class="fas fa-sync-alt mr-2"></i>Refresh
            </button>
            <button type="button" id="sendTestEmailBtn" class="flex-1 px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 focus:ring-2 focus:ring-yellow-500 transition-colors text-sm">
              <i class="fas fa-vial mr-2"></i>Test Email
            </button>
            <button type="button" id="sendFromPreviewBtn" class="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 transition-colors text-sm">
              <i class="fas fa-paper-plane mr-2"></i>Send Email
            </button>
            <button type="button" id="cancelPreviewBtn" class="flex-1 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 focus:ring-2 focus:ring-gray-500 transition-colors text-sm">
              <i class="fas fa-times mr-2"></i>Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  `;

  // Add modal to page
  document.body.insertAdjacentHTML('beforeend', modalHTML);

  // Add event listeners
  setupPreviewModalEventListeners(taskGid, warning, isEiger);
}

function setupPreviewModalEventListeners(taskGid, warning, isEiger) {
  const modal = document.getElementById('emailPreviewModal');
  const closeBtn = document.getElementById('closePreviewModal');
  const cancelBtn = document.getElementById('cancelPreviewBtn');
  const refreshBtn = document.getElementById('refreshPreviewBtn');
  const sendTestBtn = document.getElementById('sendTestEmailBtn');
  const sendBtn = document.getElementById('sendFromPreviewBtn');

  // Close modal handlers
  const closeModal = () => {
    modal.remove();
  };

  closeBtn.addEventListener('click', closeModal);
  cancelBtn.addEventListener('click', closeModal);

  // Click outside to close
  modal.addEventListener('click', (e) => {
    if (e.target === modal) {
      closeModal();
    }
  });

  // Refresh preview
  refreshBtn.addEventListener('click', async () => {
    try {
      Swal.fire({
        title: "Refreshing preview...",
        text: "Please wait...",
        allowOutsideClick: false,
        didOpen: () => {
          Swal.showLoading();
        },
      });

      const response = await fetch("/api/send_notification", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRFToken": getCsrfToken(),
        },
        body: JSON.stringify({
          task_gid: taskGid,
          preview_mode: true,
        }),
      });

      const data = await response.json();
      Swal.close();

      if (data.success && data.preview) {
        // Update preview data (but preserve user edits to subject and recipients)
        document.getElementById('emailBodyEditor').value = data.preview.body;

        Swal.fire({
          icon: "success",
          title: "Preview Refreshed",
          text: "Email preview has been updated with latest data",
          timer: 2000,
          showConfirmButton: false,
        });
      } else {
        throw new Error(data.message || "Failed to refresh preview");
      }
    } catch (error) {
      Swal.close();
      Swal.fire({
        icon: "error",
        title: "Error",
        text: error.message || "Failed to refresh preview",
        customClass: {
          popup: "rounded-lg",
        },
      });
    }
  });

  // Send test email
  sendTestBtn.addEventListener('click', async () => {
    await sendEmailFromPreview(true, closeModal);
  });

  // Send production email
  sendBtn.addEventListener('click', async () => {
    await sendEmailFromPreview(false, closeModal);
  });
}

async function sendEmailFromPreview(testMode, closeModal) {
  try {
    // Get edited content
    const subject = document.getElementById('previewSubject').value;
    const body = document.getElementById('emailBodyEditor').value;
    const toRecipients = document.getElementById('previewTo').value.split(',').map(email => email.trim());
    const ccRecipients = document.getElementById('previewCC').value.split(',').map(email => email.trim());

    // Prepare email data
    const emailData = {
      subject: subject,
      body: body,
      recipients: {
        to: toRecipients,
        cc: ccRecipients
      },
      test_mode: testMode
    };

    const modeText = testMode ? "test" : "production";

    Swal.fire({
      title: `Sending ${modeText} email...`,
      text: "Please wait...",
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
      },
    });

    const response = await fetch("/api/shipping_email/send_custom", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CSRFToken": getCsrfToken(),
      },
      body: JSON.stringify(emailData),
    });

    const data = await response.json();

    if (data.success) {
      closeModal();
      Swal.fire({
        icon: "success",
        title: "Success",
        text: `Email sent successfully in ${modeText} mode!`,
        customClass: {
          popup: "rounded-lg",
        },
      });
    } else {
      throw new Error(data.message || "Failed to send email");
    }
  } catch (error) {
    Swal.close();
    console.error("Send email error:", error);
    Swal.fire({
      icon: "error",
      title: "Error",
      text: error.message || "Failed to send email",
      customClass: {
        popup: "rounded-lg",
      },
    });
  }
}
