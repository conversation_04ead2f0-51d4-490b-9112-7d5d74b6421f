// download_files.js

// Utility function to get CSRF token
function getCsrfToken() {
  const token = document.querySelector('meta[name="csrf-token"]')?.content;
  if (!token) throw new Error("CSRF token not found");
  return token;
}

// Utility function for making API calls
async function apiCall(url, method, data = null) {
  const options = {
    method: method,
    headers: {
      "Content-Type": "application/json",
      "X-CSRFToken": getCsrfToken(),
      Accept: "application/json",
    },
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  const response = await fetch(url, options);
  if (!response.ok) {
    throw new Error(`API call failed: ${response.statusText}`);
  }
  return response.json();
}

// Print Label handler
async function handlePrint(printerIp, copies) {
  try {
    const result = await Swal.fire({
      title: "Confirm Print",
      html: `
                    <div class="text-left">
                        <p>Printer IP: ${printerIp}</p>
                        <p>Copies: ${copies}</p>
                    </div>
                `,
      icon: "question",
      showCancelButton: true,
      confirmButtonText: "Print",
      cancelButtonText: "Cancel",
    });

    if (!result.isConfirmed) return;

    const response = await apiCall("/print_to_honeywell", "POST", {
      printer_ip: printerIp,
      copy_number: parseInt(copies),
    });

    if (response.success) {
      Swal.fire({
        icon: "success",
        title: "Success",
        text: `Printed ${copies} copies successfully`,
      });
    } else {
      throw new Error(response.message || "Print failed");
    }
  } catch (error) {
    console.error("Print error:", error);
    Swal.fire({
      icon: "error",
      title: "Print Failed",
      text: error.message || "Failed to print label",
    });
  }
}

// Initialize event listeners
document.addEventListener("DOMContentLoaded", function () {
  // Print button handler
  const printButton = document.getElementById("printButton");
  if (printButton) {
    printButton.addEventListener("click", async () => {
      const printerIp = document.getElementById("printer-ip").value;
      const copies = document.getElementById("copies").value;
      await handlePrint(printerIp, copies);
    });
  }

  // Test printer connection handler
  const testConnectionButton = document.getElementById("testConnectionButton");
  if (testConnectionButton) {
    testConnectionButton.addEventListener("click", async () => {
      const printerIp = document.getElementById("printer-ip").value;
      try {
        const response = await apiCall("/test_printer_connection", "POST", {
          printer_ip: printerIp,
        });

        Swal.fire({
          icon: response.success ? "success" : "error",
          title: response.success ? "Connected" : "Connection Failed",
          text: response.message,
        });
      } catch (error) {
        console.error("Connection test error:", error);
        Swal.fire({
          icon: "error",
          title: "Connection Failed",
          text: error.message || "Failed to test printer connection",
        });
      }
    });
  }

  // Address selection handler - only initialize if packing slip section exists
  const addressButtons = document.querySelectorAll(".address-btn");
  const downloadPackingSlipBtn = document.getElementById(
    "download-packing-slip"
  );
  let selectedOrigin = "france"; // Default to France

  // Only set up address buttons if they exist (for non-Substrate-wafer labels)
  if (addressButtons.length > 0 && downloadPackingSlipBtn) {
    addressButtons.forEach((button) => {
      button.addEventListener("click", function () {
        // Remove active class from all buttons
        addressButtons.forEach((btn) => {
          btn.classList.remove("active");
          btn.classList.remove(
            "bg-indigo-600",
            "text-white",
            "hover:bg-indigo-700"
          );
          btn.classList.add(
            "bg-white",
            "text-gray-700",
            "hover:bg-gray-50",
            "border-gray-300"
          );
        });

        // Add active class to clicked button
        this.classList.add("active");
        this.classList.remove(
          "bg-white",
          "text-gray-700",
          "hover:bg-gray-50",
          "border-gray-300"
        );
        this.classList.add(
          "bg-indigo-600",
          "text-white",
          "hover:bg-indigo-700"
        );

        // Set the selected origin
        if (this.id === "france-address") {
          selectedOrigin = "france";
        } else {
          selectedOrigin = "switzerland";
        }

        // Update download link with query parameter
        if (downloadPackingSlipBtn) {
          // Get the base URL for the download_packing_slip route
          const baseUrl = "/download_packing_slip";
          downloadPackingSlipBtn.href = `${baseUrl}?origin=${selectedOrigin}`;
        }
      });
    });
  }

  // Send Email handler - now shows preview first
  const sendEmailBtn = document.getElementById("sendEmailBtn");
  if (sendEmailBtn) {
    sendEmailBtn.addEventListener("click", async () => {
      await showEmailPreview();
    });
  }

  // Keep the old direct send functionality for backward compatibility
  const directSendEmailBtn = document.getElementById("directSendEmailBtn");
  if (directSendEmailBtn) {
    directSendEmailBtn.addEventListener("click", async () => {
      try {
        // Get task GID from sessionStorage if available
        let taskGid = sessionStorage.getItem("current_task_gid");
        let asanaLink = taskGid;

        // Show input dialog for Asana link if not in session
        if (!asanaLink) {
          const { value: inputLink, isConfirmed } = await Swal.fire({
            title: "Send Email Notification",
            html: `
              <div class="text-left">
                <label class="block text-sm font-medium text-gray-700 mb-2">Asana Task Link</label>
                <input id="swal-input-asana-email" class="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500" placeholder="Paste your Asana task link here">
              </div>
            `,
            showCancelButton: true,
            confirmButtonText: "Send Email",
            confirmButtonColor: "#3b82f6",
            cancelButtonColor: "#6b7280",
            preConfirm: () => {
              const input = document.getElementById("swal-input-asana-email");
              if (!input.value.trim()) {
                Swal.showValidationMessage("Please enter an Asana task link");
                return false;
              }
              return input.value;
            },
          });

          if (!isConfirmed || !inputLink) return;
          asanaLink = inputLink;

          // Extract the task GID from various Asana URL formats
          // Check if asanaLink is just a number (direct GID input)
          if (/^\d+$/.test(asanaLink.trim())) {
            taskGid = asanaLink.trim();
          } else {
            // Try all possible Asana URL patterns
            const taskGidMatch =
              asanaLink.match(/asana\.com\/\d+\/\d+\/(\d+)(?:\/[a-z])?/) ||
              asanaLink.match(
                /app\.asana\.com\/\d+\/\d+\/\d+\/(\d+)(?:\/[a-z])?/
              ) ||
              asanaLink.match(/\/task\/(\d+)/) ||
              asanaLink.match(/\/(\d+)\?/) ||
              asanaLink.match(/\/(\d+)$/) ||
              asanaLink.match(/\/project\/\d+\/task\/(\d+)/) ||
              asanaLink.match(/app\.asana\.com\/\d+\/\d+\/\d+\/\d+\/(\d+)/);
            taskGid = taskGidMatch ? taskGidMatch[1] : null;
          }

          if (!taskGid) {
            throw new Error(
              "Invalid Asana task link. Please provide a valid link."
            );
          }
        }

        // Check if this is an Eiger project by making a preliminary call
        const checkResponse = await apiCall(
          `/api/get_task_info?task_gid=${taskGid}`,
          "GET"
        );

        if (!checkResponse.success) {
          throw new Error(
            checkResponse.message || "Failed to get task information"
          );
        }

        // Check if it's an Eiger project by looking at Lot project field
        const lotProject = checkResponse.task_info["Lot project"] || "";
        const isEigerProject = lotProject.toLowerCase().includes("eiger");

        // If not an Eiger project, show a confirmation dialog first
        if (!isEigerProject) {
          const { isConfirmed } = await Swal.fire({
            icon: "warning",
            title: "Non-Eiger Shipment",
            html: `
              <div class="text-left">
                <p class="font-semibold mb-2">This does not appear to be an Eiger shipment.</p>
                <p class="mb-4">The Lot project field does not contain 'Eiger'.</p>
                <p>Do you want to send the email notification anyway?</p>
              </div>
            `,
            showCancelButton: true,
            confirmButtonText: "Yes, Send Anyway",
            cancelButtonText: "Cancel",
            confirmButtonColor: "#f59e0b",
            cancelButtonColor: "#6b7280",
          });

          if (!isConfirmed) {
            return; // User canceled the operation
          }
        }

        Swal.fire({
          title: "Sending notification...",
          text: "Please wait...",
          allowOutsideClick: false,
          didOpen: () => {
            Swal.showLoading();
          },
        });

        const response = await apiCall("/api/send_notification", "POST", {
          task_gid: taskGid,
        });

        if (response.success) {
          // Check if it's not an Eiger project but sent anyway
          if (response.warning && !response.is_eiger) {
            await Swal.fire({
              icon: "warning",
              title: "Non-Eiger Shipment",
              html: `
                <div class="text-left">
                  <p class="mb-4">Warning: ${response.warning}</p>
                  <p>The email notification was sent successfully, but please verify this was intentional.</p>
                </div>
              `,
              confirmButtonColor: "#f59e0b",
            });
          } else {
            Swal.fire({
              icon: "success",
              title: "Success",
              text: "Email notification sent successfully!",
            });
          }
        } else if (
          response.message &&
          response.message.includes("tracking number")
        ) {
          await Swal.fire({
            icon: "warning",
            title: "Tracking Number Required",
            text: "Please ensure the UPS Tracking Number is filled in Asana before sending the notification.",
            confirmButtonColor: "#f59e0b",
          });
        } else {
          throw new Error(response.message || "Failed to send notification");
        }
      } catch (error) {
        console.error("Email error:", error);
        Swal.fire({
          icon: "error",
          title: "Error",
          text: error.message || "Failed to send email notification",
        });
      }
    });
  }

  // Test Email handler
  const testEmailBtn = document.getElementById("testEmailBtn");
  if (testEmailBtn) {
    testEmailBtn.addEventListener("click", async () => {
      try {
        // Get task GID from session or prompt for Asana task link to test Eiger verification
        const { value: asanaLink, isConfirmed } = await Swal.fire({
          title: "Send Test Email",
          html: `
            <div class="text-left">
              <label class="block text-sm font-medium text-gray-700 mb-2">Asana Task Link (Optional)</label>
              <input id="swal-input-asana-test" class="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500" placeholder="Paste your Asana task link here to test Eiger verification">
              <p class="mt-2 text-xs text-gray-500">If provided, the system will verify if this is an Eiger shipment before sending the test email.</p>
            </div>
          `,
          showCancelButton: true,
          confirmButtonText: "Send Test Email",
          confirmButtonColor: "#3b82f6",
          cancelButtonColor: "#6b7280",
          customClass: {
            popup: "rounded-lg",
          },
        });

        if (!isConfirmed) return;

        // Extract task GID if Asana link was provided
        let taskGid = null;
        if (asanaLink && typeof asanaLink === "string") {
          // Try all possible Asana URL patterns
          const taskGidMatch =
            asanaLink.match(/asana\.com\/\d+\/\d+\/(\d+)(?:\/[a-z])?/) ||
            asanaLink.match(
              /app\.asana\.com\/\d+\/\d+\/\d+\/(\d+)(?:\/[a-z])?/
            ) ||
            asanaLink.match(/\/task\/(\d+)/) ||
            asanaLink.match(/\/(\d+)\?/) ||
            asanaLink.match(/\/(\d+)$/) ||
            asanaLink.match(/\/project\/\d+\/task\/(\d+)/) ||
            asanaLink.match(/app\.asana\.com\/\d+\/\d+\/\d+\/\d+\/(\d+)/);
          taskGid = taskGidMatch ? taskGidMatch[1] : null;

          // Check if asanaLink is just a number (direct GID input)
          if (!taskGid && /^\d+$/.test(asanaLink.trim())) {
            taskGid = asanaLink.trim();
          }
        }

        Swal.fire({
          title: "Sending Test Email",
          html: `
            <div class="flex flex-col items-center">
              <div class="test-email-spinner mb-4"></div>
              <div class="text-sm text-gray-500">Sending test email to verify email functionality...</div>
            </div>
          `,
          showConfirmButton: false,
          allowOutsideClick: false,
          allowEscapeKey: false,
          didOpen: () => {
            const style = document.createElement("style");
            style.textContent = `
              .test-email-spinner {
                width: 48px;
                height: 48px;
                border: 5px solid #dbeafe;
                border-top: 5px solid #3b82f6;
                border-radius: 50%;
                animation: spin 1s linear infinite;
              }
              @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
              }
            `;
            Swal.getPopup().appendChild(style);
          },
        });

        // Add a small delay to show the loading animation
        await new Promise((resolve) => setTimeout(resolve, 800));

        // Check if it's an Eiger project
        if (taskGid) {
          const checkResponse = await apiCall(
            `/api/get_task_info?task_gid=${taskGid}`,
            "GET"
          );

          if (checkResponse.success) {
            // Check if it's an Eiger project by looking at Lot project field
            const lotProject = checkResponse.task_info["Lot project"] || "";
            const isEigerProject = lotProject.toLowerCase().includes("eiger");

            // If not an Eiger project, show a confirmation dialog first
            if (!isEigerProject) {
              const { isConfirmed: proceedAnyway } = await Swal.fire({
                icon: "warning",
                title: "Non-Eiger Shipment",
                html: `
                  <div class="text-left">
                    <p class="font-semibold mb-2">This does not appear to be an Eiger shipment.</p>
                    <p class="mb-4">The Lot project field does not contain 'Eiger'.</p>
                    <p>Do you want to send the test email anyway?</p>
                    <p class="mt-4 text-xs text-gray-500">Note: Test emails are only sent to admin recipients.</p>
                  </div>
                `,
                showCancelButton: true,
                confirmButtonText: "Yes, Send Anyway",
                cancelButtonText: "Cancel",
                confirmButtonColor: "#f59e0b",
                cancelButtonColor: "#6b7280",
              });

              if (!proceedAnyway) {
                return; // User canceled the operation
              }
            }
          }
        }

        // Use apiCall with data parameter to include the task_gid
        const response = await apiCall("/api/test_email", "POST", {
          task_gid: taskGid,
        });

        if (response.warning && !response.is_eiger) {
          await Swal.fire({
            icon: "warning",
            title: "Non-Eiger Test Email Sent",
            html: `
              <div class="text-left">
                <p class="mb-4">Warning: ${response.warning}</p>
                <p>The test email was sent successfully to admin recipients only.</p>
                <p class="mt-4 text-xs text-gray-500">Remember: This was just a test and only went to admin emails.</p>
              </div>
            `,
            confirmButtonColor: "#f59e0b",
          });
        } else {
          Swal.fire({
            icon: "success",
            title: "Success",
            text: "Test email sent successfully!",
          });
        }
      } catch (error) {
        console.error("Test email error:", error);
        Swal.fire({
          icon: "error",
          title: "Error",
          text: error.message || "Failed to send test email",
        });
      }
    });
  }
});

// Generate Eiger CSV script handler
document.addEventListener("DOMContentLoaded", function () {
  const eigerCsvBtn = document.getElementById("eigerCsvBtn");

  async function generateEigerCsv() {
    try {
      // Get task GID from sessionStorage if available
      let asanaLink = sessionStorage.getItem("current_task_gid");

      // Show input dialog for Asana link if not in session
      if (!asanaLink) {
        const { value: inputLink } = await Swal.fire({
          title: "Enter Asana Task Link",
          input: "text",
          inputLabel: "Asana Link",
          inputPlaceholder: "Paste your Asana task link here",
          showCancelButton: true,
          inputValidator: (value) => {
            if (!value) {
              return "Please enter an Asana task link";
            }
          },
        });

        if (!inputLink) return;
        asanaLink = inputLink;
      }

      // If it's just a GID, convert to full URL
      if (asanaLink && !asanaLink.includes("https://")) {
        asanaLink = `https://app.asana.com/0/0/${asanaLink}`;
      }

      // Show loading state
      Swal.fire({
        title: "Generating CSV...",
        text: "Please wait...",
        allowOutsideClick: false,
        didOpen: () => {
          Swal.showLoading();
        },
      });

      const response = await fetch("/generate_eiger_csv", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRFToken": document.querySelector('meta[name="csrf-token"]')
            .content,
        },
        body: JSON.stringify({
          asana_link: asanaLink,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to generate CSV");
      }

      // Create blob and download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;

      // Get the filename from the Content-Disposition header if available
      let serverFilename = "";

      // Extract the filename from the Content-Disposition header
      const contentDisposition = response.headers.get("Content-Disposition");
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
        if (filenameMatch && filenameMatch[1]) {
          serverFilename = filenameMatch[1];
          console.log("Using server-provided filename:", serverFilename);
        }
      }

      // If we got a server filename, use it
      if (serverFilename) {
        a.download = serverFilename;
      } else {
        // Fall back to client-side filename generation
        console.log("No server filename found, generating locally");

        // Get the task GID from the session or input field
        let taskGid =
          document.querySelector('[name="task_gid"]')?.value ||
          sessionStorage.getItem("current_task_gid");

        // If we have an Asana link in the input field, extract the task GID
        if (asanaLink && !taskGid) {
          // Check if asanaLink is just a number (direct GID input)
          if (/^\d+$/.test(asanaLink.trim())) {
            taskGid = asanaLink.trim();
          } else {
            // Try all possible Asana URL patterns
            const taskGidMatch =
              asanaLink.match(/asana\.com\/\d+\/\d+\/(\d+)(?:\/[a-z])?/) ||
              asanaLink.match(
                /app\.asana\.com\/\d+\/\d+\/\d+\/(\d+)(?:\/[a-z])?/
              ) ||
              asanaLink.match(/\/task\/(\d+)/) ||
              asanaLink.match(/\/(\d+)\?/) ||
              asanaLink.match(/\/(\d+)$/) ||
              asanaLink.match(/\/project\/\d+\/task\/(\d+)/) ||
              asanaLink.match(/app\.asana\.com\/\d+\/\d+\/\d+\/\d+\/(\d+)/);
            taskGid = taskGidMatch ? taskGidMatch[1] : null;
          }
        }

        // Create a timestamp with date and time (YYYY-MM-DD_HH-MM-SS)
        const now = new Date();
        const dateStr = now.toISOString().slice(0, 10);
        const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, "-");
        const timestamp = `${dateStr}_${timeStr}`;

        // Default filename component
        let taskName = "";

        // If we have a task GID, try to get the task name
        if (taskGid) {
          try {
            // Fetch task info to get the task name
            const taskInfoResponse = await fetch(
              `/api/get_task_info?task_gid=${taskGid}`
            );

            if (taskInfoResponse.ok) {
              const taskInfoData = await taskInfoResponse.json();
              if (
                taskInfoData.success &&
                taskInfoData.task_info &&
                taskInfoData.task_info.name
              ) {
                // Clean the task name for use in a filename (remove special characters)
                taskName = taskInfoData.task_info.name
                  .replace(/[^a-zA-Z0-9]/g, "_") // Replace special chars with underscore
                  .replace(/_+/g, "_") // Replace multiple underscores with a single one
                  .substring(0, 50); // Limit length to avoid very long filenames
              }
            }
          } catch (error) {
            console.error("Error fetching task name:", error);
            // Continue with default name if there's an error
          }
        }

        // Always prepend "Eiger_" to the filename and include date and hour separately
        // Split timestamp into date and hour parts
        const timestampParts = timestamp.split("_");
        const date = timestampParts[0];
        const hour = timestampParts[1];

        a.download = taskName
          ? `Eiger_${taskName}_${date}_${hour}.csv`
          : `Eiger_${date}_${hour}.csv`;
      }

      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      await Swal.fire({
        icon: "success",
        title: "Success",
        text: "CSV file generated successfully!",
      });
    } catch (error) {
      console.error("CSV generation error:", error);
      await Swal.fire({
        icon: "error",
        title: "Error",
        text: error.message || "Failed to generate CSV file",
      });
    }
  }

  if (eigerCsvBtn) {
    eigerCsvBtn.addEventListener("click", generateEigerCsv);
  }
});

// Email Preview Functions
async function showEmailPreview() {
  try {
    // Get task GID from sessionStorage if available
    let taskGid = sessionStorage.getItem("current_task_gid");
    let asanaLink = taskGid;

    // If no task GID in session, try to get from URL
    if (!taskGid) {
      const urlParams = new URLSearchParams(window.location.search);
      asanaLink = urlParams.get("asana_link");
      if (asanaLink) {
        taskGid = extractTaskGidFromUrl(asanaLink);
      }
    }

    if (!taskGid) {
      await Swal.fire({
        icon: "error",
        title: "Error",
        text: "No Asana task found. Please ensure you have a valid Asana task linked.",
      });
      return;
    }

    // Show loading
    Swal.fire({
      title: "Generating email preview...",
      text: "Please wait...",
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
      },
    });

    // Generate preview
    const response = await apiCall("/api/send_notification", "POST", {
      task_gid: taskGid,
      preview_mode: true,
    });

    Swal.close();

    if (response.success && response.preview) {
      // Show preview modal
      showPreviewModal(response.preview, taskGid, response.warning, response.is_eiger);
    } else {
      throw new Error(response.message || "Failed to generate preview");
    }
  } catch (error) {
    Swal.close();
    console.error("Preview error:", error);
    Swal.fire({
      icon: "error",
      title: "Error",
      text: error.message || "Failed to generate email preview",
    });
  }
}

// Helper function for API calls
async function apiCall(url, method, data) {
  const response = await fetch(url, {
    method: method,
    headers: {
      "Content-Type": "application/json",
      "X-CSRFToken": document.querySelector('meta[name="csrf-token"]')?.content,
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return await response.json();
}

function showPreviewModal(previewData, taskGid, warning, isEiger) {
  // Create modal HTML
  const modalHTML = `
    <div id="emailPreviewModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[95vh] flex flex-col">
        <!-- Modal Header -->
        <div class="bg-gradient-to-r from-indigo-600 to-indigo-700 text-white p-6 flex-shrink-0 rounded-t-lg">
          <div class="flex justify-between items-center">
            <h3 class="text-2xl font-bold">
              <i class="fas fa-eye mr-3"></i>
              Eiger Email Preview & Editor
            </h3>
            <button id="closePreviewModal" class="text-white hover:text-indigo-200 transition-colors">
              <i class="fas fa-times text-2xl"></i>
            </button>
          </div>
          <p class="text-indigo-100 mt-2 text-sm">
            Review and customize your Eiger shipping notification before sending
          </p>
          ${warning ? `
            <div class="mt-3 p-3 bg-yellow-500 bg-opacity-20 border border-yellow-400 rounded-md">
              <div class="flex items-center">
                <i class="fas fa-exclamation-triangle text-yellow-300 mr-2"></i>
                <span class="text-yellow-100 text-sm">${warning}</span>
              </div>
            </div>
          ` : ''}
        </div>

        <!-- Modal Body -->
        <div class="p-6 overflow-y-auto flex-1 min-h-0">
          <!-- Email Details -->
          <div class="space-y-6 mb-8">
            <!-- Subject Line -->
            <div class="form-group">
              <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                <i class="fas fa-edit mr-2 text-blue-600"></i>Subject Line (Editable)
              </label>
              <input type="text" id="previewSubject" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                     value="${previewData.subject}" />
            </div>

            <!-- Recipients -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="form-group">
                <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                  <i class="fas fa-users mr-2 text-green-600"></i>To Recipients (Editable)
                </label>
                <input type="text" id="previewTo" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                       value="${previewData.recipients.to.join(', ')}" />
              </div>
              <div class="form-group">
                <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                  <i class="fas fa-user-plus mr-2 text-purple-600"></i>CC Recipients (Editable)
                </label>
                <input type="text" id="previewCC" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                       value="${previewData.recipients.cc.join(', ')}" />
              </div>
            </div>
          </div>

          <!-- Email Body Editor -->
          <div class="form-group mb-6">
            <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
              <i class="fas fa-edit mr-2 text-blue-600"></i>Email Body (Fully Editable)
            </label>
            <div class="border-2 border-gray-200 dark:border-gray-600 rounded-xl overflow-hidden">
              <textarea id="emailBodyEditor" class="w-full min-h-[300px] p-6 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                        style="line-height: 1.8; font-size: 15px; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">${previewData.body}</textarea>
            </div>
            <div class="mt-4 p-4 bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg">
              <div class="flex items-start">
                <i class="fas fa-magic text-blue-600 dark:text-blue-400 mr-3 mt-1"></i>
                <div>
                  <h5 class="font-semibold text-blue-800 dark:text-blue-200 mb-1">
                    Maximum Editing Flexibility
                  </h5>
                  <p class="text-sm text-blue-700 dark:text-blue-300">
                    Edit any part of the email including subject, recipients, body content, tracking information, and signature. All changes will be reflected in the final email.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Modal Footer -->
        <div class="border-t border-gray-200 dark:border-gray-600 p-6 bg-gray-50 dark:bg-gray-800 flex-shrink-0 rounded-b-lg">
          <div class="flex flex-col sm:flex-row gap-3">
            <button type="button" id="refreshPreviewBtn" class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 transition-colors text-sm">
              <i class="fas fa-sync-alt mr-2"></i>Refresh
            </button>
            <button type="button" id="sendTestEmailBtn" class="flex-1 px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 focus:ring-2 focus:ring-yellow-500 transition-colors text-sm">
              <i class="fas fa-vial mr-2"></i>Test Email
            </button>
            <button type="button" id="sendFromPreviewBtn" class="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 transition-colors text-sm">
              <i class="fas fa-paper-plane mr-2"></i>Send Email
            </button>
            <button type="button" id="cancelPreviewBtn" class="flex-1 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 focus:ring-2 focus:ring-gray-500 transition-colors text-sm">
              <i class="fas fa-times mr-2"></i>Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  `;

  // Add modal to page
  document.body.insertAdjacentHTML('beforeend', modalHTML);

  // Add event listeners
  setupPreviewModalEventListeners(taskGid, warning, isEiger);
}

function setupPreviewModalEventListeners(taskGid, warning, isEiger) {
  const modal = document.getElementById('emailPreviewModal');
  const closeBtn = document.getElementById('closePreviewModal');
  const cancelBtn = document.getElementById('cancelPreviewBtn');
  const refreshBtn = document.getElementById('refreshPreviewBtn');
  const sendTestBtn = document.getElementById('sendTestEmailBtn');
  const sendBtn = document.getElementById('sendFromPreviewBtn');

  // Close modal handlers
  const closeModal = () => {
    modal.remove();
  };

  closeBtn.addEventListener('click', closeModal);
  cancelBtn.addEventListener('click', closeModal);

  // Click outside to close
  modal.addEventListener('click', (e) => {
    if (e.target === modal) {
      closeModal();
    }
  });

  // Refresh preview
  refreshBtn.addEventListener('click', async () => {
    try {
      Swal.fire({
        title: "Refreshing preview...",
        text: "Please wait...",
        allowOutsideClick: false,
        didOpen: () => {
          Swal.showLoading();
        },
      });

      const response = await apiCall("/api/send_notification", "POST", {
        task_gid: taskGid,
        preview_mode: true,
      });

      Swal.close();

      if (response.success && response.preview) {
        // Update preview data (but preserve user edits to subject and recipients)
        document.getElementById('emailBodyEditor').value = response.preview.body;

        Swal.fire({
          icon: "success",
          title: "Preview Refreshed",
          text: "Email preview has been updated with latest data",
          timer: 2000,
          showConfirmButton: false,
        });
      } else {
        throw new Error(response.message || "Failed to refresh preview");
      }
    } catch (error) {
      Swal.close();
      Swal.fire({
        icon: "error",
        title: "Error",
        text: error.message || "Failed to refresh preview",
      });
    }
  });

  // Send test email
  sendTestBtn.addEventListener('click', async () => {
    await sendEmailFromPreview(true, closeModal);
  });

  // Send production email
  sendBtn.addEventListener('click', async () => {
    await sendEmailFromPreview(false, closeModal);
  });
}

async function sendEmailFromPreview(testMode, closeModal) {
  try {
    // Get edited content
    const subject = document.getElementById('previewSubject').value;
    const body = document.getElementById('emailBodyEditor').value;
    const toRecipients = document.getElementById('previewTo').value.split(',').map(email => email.trim());
    const ccRecipients = document.getElementById('previewCC').value.split(',').map(email => email.trim());

    // Prepare email data
    const emailData = {
      subject: subject,
      body: body,
      recipients: {
        to: toRecipients,
        cc: ccRecipients
      },
      test_mode: testMode
    };

    const modeText = testMode ? "test" : "production";

    Swal.fire({
      title: `Sending ${modeText} email...`,
      text: "Please wait...",
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
      },
    });

    const response = await apiCall("/api/shipping_email/send_custom", "POST", emailData);

    if (response.success) {
      closeModal();
      Swal.fire({
        icon: "success",
        title: "Success",
        text: `Email sent successfully in ${modeText} mode!`,
      });
    } else {
      throw new Error(response.message || "Failed to send email");
    }
  } catch (error) {
    Swal.close();
    console.error("Send email error:", error);
    Swal.fire({
      icon: "error",
      title: "Error",
      text: error.message || "Failed to send email",
    });
  }
}
