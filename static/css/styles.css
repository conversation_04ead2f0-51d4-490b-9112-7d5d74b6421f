/* Global theme variables */
:root {
  --bg-primary-light: #ffffff;
  --bg-secondary-light: #f8f9fa;
  --text-primary-light: #212529;
  --text-secondary-light: #495057;
  --border-light: #dee2e6;
  --input-bg-light: #f8f9fa;
  --input-border-light: #ced4da;
  --btn-primary-light: #007bff;
  --btn-secondary-light: #6c757d;
  --btn-danger-light: #dc3545;
  --table-border-light: #dee2e6;
  --modal-bg-light: #ffffff;

  /* Dark theme */
  --bg-primary-dark: #1a1a1a;
  --bg-secondary-dark: #2d2d2d;
  --text-primary-dark: #f8f9fa;
  --text-secondary-dark: #adb5bd;
  --border-dark: #495057;
  --input-bg-dark: #343a40;
  --input-border-dark: #495057;
  --btn-primary-dark: #0d6efd;
  --btn-secondary-dark: #5c636a;
  --btn-danger-dark: #dc3545;
  --table-border-dark: #495057;
  --modal-bg-dark: #343a40;
}

/* Form groups */
.form-group {
  margin-bottom: 20px;
}

/* Form inputs */
input,
select,
textarea {
  width: 100%;
  padding: 10px;
  border-radius: 6px;
  border: 1px solid var(--input-border-light);
  font-size: 16px;
  background-color: var(--input-bg-light);
  color: var(--text-primary-light);
  transition: background-color 0.3s, color 0.3s, border-color 0.3s;
}

textarea[readonly],
input[readonly],
select[disabled] {
  background-color: #e9ecef;
  cursor: not-allowed;
}

.form-label {
  font-size: 1.1rem;
  color: var(--text-primary-light);
  transition: color 0.3s;
}

/* Buttons */
.btn {
  padding: 10px 20px;
  margin-top: 15px;
  transition: background-color 0.3s, border-color 0.3s;
}

.btn-primary {
  background-color: var(--btn-primary-light);
  border-color: var(--btn-primary-light);
  color: white;
}

.btn-secondary {
  background-color: var(--btn-secondary-light);
  border-color: var(--btn-secondary-light);
  color: white;
}

.btn-danger {
  background-color: var(--btn-danger-light);
  border-color: var(--btn-danger-light);
  color: white;
}

/* Tables */
.table {
  color: var(--text-primary-light);
  transition: color 0.3s;
}

.table-bordered {
  border: 1px solid var(--table-border-light);
}

.table-bordered th,
.table-bordered td {
  padding: 15px;
  text-align: left;
  vertical-align: middle;
  border-color: var(--table-border-light);
}

.table-responsive {
  margin-top: 20px;
}

/* Modals */
.modal-title {
  font-weight: bold;
  color: var(--text-primary-light);
}

.modal-content {
  padding: 20px;
  background-color: var(--modal-bg-light);
  transition: background-color 0.3s;
}

/* Preview */
#previewIframe {
  border-radius: 5px;
  border: 1px solid var(--border-light);
}

.iframe-style {
  height: 500px;
}

/* Logo */
.logo {
  height: auto;
  max-height: 80px;
  width: auto;
  max-width: 150px;
}

/* Dark mode styles */
.dark input,
.dark select,
.dark textarea {
  background-color: var(--input-bg-dark);
  border-color: var(--input-border-dark);
  color: var(--text-primary-dark);
}

.dark .form-label {
  color: var(--text-primary-dark);
}

.dark .btn-primary {
  background-color: var(--btn-primary-dark);
  border-color: var(--btn-primary-dark);
}

.dark .btn-secondary {
  background-color: var(--btn-secondary-dark);
  border-color: var(--btn-secondary-dark);
}

.dark .btn-danger {
  background-color: var(--btn-danger-dark);
  border-color: var(--btn-danger-dark);
}

.dark .table {
  color: var(--text-primary-dark);
}

.dark .table-bordered {
  border-color: var(--table-border-dark);
}

.dark .table-bordered th,
.dark .table-bordered td {
  border-color: var(--table-border-dark);
}

.dark .modal-title {
  color: var(--text-primary-dark);
}

.dark .modal-content {
  background-color: var(--modal-bg-dark);
}

.dark #previewIframe {
  border-color: var(--border-dark);
}

/* Transition all elements */
.dark-transition * {
  transition: background-color 0.3s, color 0.3s, border-color 0.3s;
}

/* Smart Wafer Validation Styles */
.validation-container {
  margin-top: 8px;
  padding: 10px;
  border-radius: 6px;
  font-size: 14px;
  border-left: 4px solid;
  background-color: #f8f9fa;
}

.validation-container.success {
  border-left-color: #28a745;
  background-color: #d4edda;
  color: #155724;
}

.validation-container.warning {
  border-left-color: #ffc107;
  background-color: #fff3cd;
  color: #856404;
}

.validation-container.error {
  border-left-color: #dc3545;
  background-color: #f8d7da;
  color: #721c24;
}

.validation-container.info {
  border-left-color: #17a2b8;
  background-color: #d1ecf1;
  color: #0c5460;
}

.validation-container.loading {
  border-left-color: #6c757d;
  background-color: #e9ecef;
  color: #495057;
}

.validation-message {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.validation-icon {
  margin-right: 8px;
  font-size: 16px;
}

.validation-text {
  flex: 1;
}

.btn-sync-wafer,
.btn-details {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  padding: 4px 8px;
  margin: 2px 4px 2px 0;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-sync-wafer:hover,
.btn-details:hover {
  background: rgba(255, 255, 255, 1);
  border-color: rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
}

.btn-sync-wafer {
  color: #007bff;
}

.btn-details {
  color: #6c757d;
}

.btn-sync-wafer:hover {
  color: #0056b3;
}

.btn-details:hover {
  color: #495057;
}

/* Ultra Modern Icarium Sync Widget Styles */
.icarium-sync-widget {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 24px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 16px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  overflow: hidden;
  margin-bottom: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.icarium-sync-widget:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.15),
    0 16px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.icarium-sync-widget.needs-attention {
  animation: elegantPulse 3s ease-in-out infinite;
}

@keyframes elegantPulse {
  0%, 100% {
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.1),
      0 8px 16px rgba(0, 0, 0, 0.05),
      0 0 0 0 rgba(239, 68, 68, 0);
  }
  50% {
    box-shadow:
      0 24px 48px rgba(239, 68, 68, 0.2),
      0 12px 24px rgba(239, 68, 68, 0.1),
      0 0 0 8px rgba(239, 68, 68, 0.1);
  }
}

.icarium-sync-widget::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(
    90deg,
    #ff6b6b 0%,
    #4ecdc4 25%,
    #45b7d1 50%,
    #96ceb4 75%,
    #feca57 100%
  );
  background-size: 400% 100%;
  animation: rainbowFlow 4s ease-in-out infinite;
}

@keyframes rainbowFlow {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 28px 32px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.widget-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.widget-title {
  margin: 0;
  font-size: 22px;
  font-weight: 800;
  display: flex;
  align-items: center;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  letter-spacing: -0.5px;
}

.widget-title i {
  margin-right: 16px;
  font-size: 28px;
  background: linear-gradient(45deg, #ffd700, #ffed4e, #ffd700);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
  animation: iconGlow 2s ease-in-out infinite;
  filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.3));
}

@keyframes iconGlow {
  0%, 100% {
    background-position: 0% 50%;
    filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.3));
  }
  50% {
    background-position: 100% 50%;
    filter: drop-shadow(0 0 16px rgba(255, 215, 0, 0.6));
  }
}

.widget-actions {
  display: flex;
  gap: 16px;
}

.btn-refresh,
.btn-settings {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  color: white;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);
  font-size: 14px;
  font-weight: 600;
  position: relative;
  overflow: hidden;
}

.btn-refresh::before,
.btn-settings::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-refresh:hover,
.btn-settings:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2));
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.btn-refresh:hover::before,
.btn-settings:hover::before {
  left: 100%;
}

.btn-refresh:hover,
.btn-settings:hover {
  background: rgba(255, 255, 255, 0.3);
}

.widget-content {
  padding: 32px;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  color: #1a202c;
  position: relative;
}

.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 32px;
  color: #4a5568;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.loading-state .loading-icon,
.error-state .error-icon {
  margin-bottom: 20px;
}

.loading-state .loading-icon i,
.error-state .error-icon i {
  font-size: 40px;
  color: #667eea;
  animation: spin 2s linear infinite;
}

.error-state .error-icon i {
  color: #ef4444;
  animation: none;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-state .loading-text,
.error-state .error-text {
  text-align: center;
  margin-bottom: 24px;
}

.loading-state .loading-text h4,
.error-state .error-text h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 700;
  color: #1a202c;
}

.loading-state .loading-text p,
.error-state .error-text p {
  margin: 0;
  font-size: 14px;
  color: #4a5568;
  opacity: 0.8;
}

.error-state {
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.error-state .error-text h4 {
  color: #ef4444;
}

.btn-retry {
  margin-top: 16px;
  padding: 10px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-retry:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.sync-status {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.status-header {
  display: flex;
  align-items: center;
  gap: 20px;
  font-weight: 800;
  font-size: 20px;
  padding: 24px;
  border-radius: 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f7fafc 100%);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.status-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, currentColor, transparent);
  opacity: 0.3;
}

.status-header .status-icon {
  font-size: 32px;
  padding: 16px;
  border-radius: 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f7fafc 100%);
  box-shadow:
    0 8px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  position: relative;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.status-header .status-text {
  flex: 1;
  margin-left: 8px;
}

.status-header .status-text h4 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 700;
  color: #1a202c;
}

.status-header .status-text p {
  margin: 0;
  font-size: 14px;
  color: #4a5568;
  opacity: 0.8;
}

.status-header .status-icon::before {
  content: "";
  position: absolute;
  inset: -2px;
  border-radius: 22px;
  padding: 2px;
  background: linear-gradient(135deg, currentColor, transparent, currentColor);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  opacity: 0.3;
}

.sync-status.success .status-icon {
  color: #10b981;
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  animation: successPulse 2s ease-in-out infinite;
}

.sync-status.warning .status-icon {
  color: #f59e0b;
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
  animation: warningPulse 2s ease-in-out infinite;
}

.sync-status.error .status-icon {
  color: #ef4444;
  background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
  animation: errorPulse 2s ease-in-out infinite;
}

.sync-status.info .status-icon {
  color: #3b82f6;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  animation: infoPulse 2s ease-in-out infinite;
}

@keyframes successPulse {
  0%, 100% { box-shadow: 0 8px 16px rgba(16, 185, 129, 0.2); }
  50% { box-shadow: 0 12px 24px rgba(16, 185, 129, 0.4); }
}

@keyframes warningPulse {
  0%, 100% { box-shadow: 0 8px 16px rgba(245, 158, 11, 0.2); }
  50% { box-shadow: 0 12px 24px rgba(245, 158, 11, 0.4); }
}

@keyframes errorPulse {
  0%, 100% { box-shadow: 0 8px 16px rgba(239, 68, 68, 0.2); }
  50% { box-shadow: 0 12px 24px rgba(239, 68, 68, 0.4); }
}

@keyframes infoPulse {
  0%, 100% { box-shadow: 0 8px 16px rgba(59, 130, 246, 0.2); }
  50% { box-shadow: 0 12px 24px rgba(59, 130, 246, 0.4); }
}

.sync-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  margin: 24px 0;
}

.metric {
  text-align: center;
  padding: 28px 24px;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.metric::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.metric:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 16px 48px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.metric:hover::before {
  transform: scaleX(1);
}

.metric-value {
  font-size: 40px;
  font-weight: 900;
  color: #1a202c;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
  animation: gradientShift 3s ease-in-out infinite;
  margin-bottom: 12px;
  line-height: 1;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.metric-label {
  font-size: 13px;
  color: #4a5568;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
}

.metric-label::after {
  content: "";
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 24px;
  height: 2px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 1px;
}

.lots-summary {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  padding: 20px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.lots-header {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 700;
  margin-bottom: 16px;
  color: #2d3748;
  font-size: 16px;
}

.lots-header i {
  font-size: 18px;
  color: #667eea;
}

.lots-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.lot-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  font-size: 14px;
  border: 1px solid rgba(102, 126, 234, 0.1);
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.lot-item:hover {
  transform: translateX(8px) translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.3);
}

.lot-item .lot-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.lot-id {
  font-weight: 700;
  color: #1a202c;
  font-size: 15px;
}

.lot-status {
  font-size: 12px;
  color: #f59e0b;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.lot-count {
  color: #667eea;
  font-size: 14px;
  font-weight: 700;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(102, 126, 234, 0.2));
  padding: 8px 12px;
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.action-buttons {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  margin-top: 24px;
}

.btn-action {
  flex: 1;
  min-width: 160px;
  padding: 16px 24px;
  border: none;
  border-radius: 16px;
  font-weight: 700;
  font-size: 15px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.btn-action::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.btn-action::after {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 16px;
  padding: 1px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.4), transparent, rgba(255, 255, 255, 0.4));
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
}

.btn-action:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow:
    0 16px 40px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.btn-action:hover::before {
  left: 100%;
}

.btn-action:active {
  transform: translateY(-2px) scale(0.98);
}

.btn-action:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn-action i {
  font-size: 16px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  box-shadow:
    0 16px 40px rgba(102, 126, 234, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.btn-secondary {
  background: linear-gradient(135deg, #64748b 0%, #475569 100%);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover:not(:disabled) {
  background: linear-gradient(135deg, #52525b 0%, #3f3f46 100%);
  box-shadow:
    0 16px 40px rgba(100, 116, 139, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.btn-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-success:hover:not(:disabled) {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  box-shadow:
    0 16px 40px rgba(16, 185, 129, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* Dark Mode Support for Icarium Sync Widget */
.dark .icarium-sync-widget {
  background: linear-gradient(145deg, #1a202c 0%, #2d3748 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 8px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.dark .widget-header {
  background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
}

.dark .widget-content {
  background: linear-gradient(145deg, #1a202c 0%, #2d3748 100%);
  color: #f7fafc;
}

.dark .loading-state,
.dark .error-state {
  background: linear-gradient(145deg, #1a202c 0%, #2d3748 100%);
  color: #e2e8f0;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .status-header {
  background: linear-gradient(145deg, #1a202c 0%, #2d3748 100%);
  color: #f7fafc;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .metric {
  background: linear-gradient(145deg, #1a202c 0%, #2d3748 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.dark .metric-value {
  color: #f7fafc;
}

.dark .metric-label {
  color: #cbd5e0;
}

.dark .lots-summary {
  background: linear-gradient(145deg, #1a202c 0%, #2d3748 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.dark .lots-header {
  color: #f7fafc;
}

.dark .lot-item {
  background: linear-gradient(145deg, #374151 0%, #4b5563 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .lot-id {
  color: #f7fafc;
}

.dark .lot-count {
  background: rgba(102, 126, 234, 0.3);
  color: #93c5fd;
}

/* Responsive Design for Mobile */
@media (max-width: 768px) {
  .sync-metrics {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .btn-action {
    min-width: auto;
    width: 100%;
  }

  .widget-header {
    padding: 16px 20px;
  }

  .widget-title {
    font-size: 16px;
  }

  .metric-value {
    font-size: 24px;
  }
}

/* Enhanced Connection Status Styling */
.connection-status {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.connection-status.connected {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.connection-status.disconnected {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
  animation: pulse 2s infinite;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.connection-status.error {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.connection-status:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

/* Header Integration Styles */
#connection-status-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

/* Ensure proper spacing in header */
.connection-status {
  margin-right: 8px;
}

.connection-status + button,
button + .connection-status {
  margin-left: 12px;
}

/* Additional spacing for header elements */
#connection-status-container + button {
  margin-left: 8px;
}

.widget-footer {
  padding: 8px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  text-align: center;
}

.last-updated {
  color: #6c757d;
  font-size: 12px;
}

/* Batch Sync Dialog Styles */
.batch-sync-dialog {
  text-align: left;
}

.batch-recommendations {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
}

.batch-rec-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #007bff;
}

.rec-info {
  flex: 1;
}

.rec-info strong {
  display: block;
  margin-bottom: 4px;
}

.rec-info p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
}

.btn-sync-lot {
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  cursor: pointer;
  font-size: 12px;
  white-space: nowrap;
}

.btn-sync-lot:hover {
  background: #0056b3;
}

/* Sync Settings Styles */
.sync-settings {
  text-align: left;
}

.setting-item {
  margin-bottom: 16px;
}

.setting-item label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.setting-item select {
  margin-top: 4px;
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 100%;
}

/* Smart Sync Dashboard Styles */
.smart-sync-dashboard {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 24px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.dashboard-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.dashboard-title i {
  margin-right: 10px;
}

.dashboard-actions {
  display: flex;
  gap: 8px;
}

.dashboard-content {
  padding: 24px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.empty-state i {
  font-size: 48px;
  color: #28a745;
  margin-bottom: 16px;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  color: #495057;
}

.sync-analytics {
  margin-bottom: 32px;
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.analytics-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border-left: 4px solid #007bff;
}

.card-icon {
  margin-right: 16px;
  font-size: 24px;
  color: #007bff;
}

.card-value {
  font-size: 28px;
  font-weight: bold;
  color: #495057;
  line-height: 1;
}

.card-label {
  font-size: 14px;
  color: #6c757d;
  margin-top: 4px;
}

.insights-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
}

.insights-section h4 {
  margin: 0 0 12px 0;
  color: #495057;
  font-size: 16px;
}

.insights-list {
  margin: 0;
  padding-left: 20px;
}

.insights-list li {
  margin-bottom: 8px;
  color: #6c757d;
}

.recommendations-section {
  margin-bottom: 32px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 20px 0;
  font-size: 18px;
  color: #495057;
}

.recommendations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.recommendation-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  position: relative;
}

.recommendation-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.recommendation-card.completed {
  background: #d4edda;
  border-color: #28a745;
}

.recommendation-card.priority-urgent {
  border-left: 4px solid #dc3545;
}

.recommendation-card.priority-high {
  border-left: 4px solid #fd7e14;
}

.recommendation-card.priority-medium {
  border-left: 4px solid #ffc107;
}

.recommendation-card.priority-low {
  border-left: 4px solid #6c757d;
}

.rec-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.rec-title {
  font-weight: 600;
  color: #495057;
  flex: 1;
}

.rec-title i {
  margin-right: 8px;
  color: #007bff;
}

.priority-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  color: white;
}

.priority-badge.priority-urgent {
  background: #dc3545;
}

.priority-badge.priority-high {
  background: #fd7e14;
}

.priority-badge.priority-medium {
  background: #ffc107;
  color: #212529;
}

.priority-badge.priority-low {
  background: #6c757d;
}

.rec-description {
  color: #6c757d;
  margin-bottom: 16px;
  line-height: 1.5;
}

.rec-metrics {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.metric {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #6c757d;
}

.metric i {
  color: #007bff;
}

.rec-benefits {
  margin-bottom: 20px;
}

.rec-benefits h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #495057;
}

.rec-benefits ul {
  margin: 0;
  padding-left: 20px;
}

.rec-benefits li {
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 4px;
}

.rec-actions {
  display: flex;
  gap: 8px;
}

.btn-execute,
.btn-details {
  flex: 1;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.btn-execute {
  background: #007bff;
  color: white;
}

.btn-execute:hover:not(:disabled) {
  background: #0056b3;
}

.btn-execute:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.btn-details {
  background: #f8f9fa;
  color: #6c757d;
  border: 1px solid #e9ecef;
}

.btn-details:hover {
  background: #e9ecef;
}

.quick-actions-section {
  border-top: 1px solid #e9ecef;
  padding-top: 24px;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px;
  border: 2px solid transparent;
  border-radius: 12px;
  background: #f8f9fa;
  color: #495057;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.quick-action-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.quick-action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quick-action-btn.urgent {
  border-color: #dc3545;
  background: #f8d7da;
}

.quick-action-btn.urgent:hover:not(:disabled) {
  background: #f5c6cb;
}

.quick-action-btn.batch {
  border-color: #007bff;
  background: #d1ecf1;
}

.quick-action-btn.batch:hover:not(:disabled) {
  background: #bee5eb;
}

.quick-action-btn.all {
  border-color: #28a745;
  background: #d4edda;
}

.quick-action-btn.all:hover:not(:disabled) {
  background: #c3e6cb;
}

.quick-action-btn.analyze {
  border-color: #6f42c1;
  background: #e2d9f3;
}

.quick-action-btn.analyze:hover:not(:disabled) {
  background: #d1c4e9;
}

.quick-action-btn i {
  font-size: 24px;
}

.quick-action-btn span {
  font-weight: 500;
  text-align: center;
}
