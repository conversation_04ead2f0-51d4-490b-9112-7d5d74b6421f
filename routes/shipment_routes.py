import re
import traceback
from datetime import datetime, timezone
from typing import Optional

import asana
from flask import Blueprint, current_app, jsonify, render_template, request
from sqlalchemy import select

from core.models.models import Shipment, ShipmentStatus, ShipmentWafer
from database.db_config import get_db_cursor, get_sqlalchemy_connection
from integrations.asana.asana_client import (
    OPTS,
    get_asana_client,
    get_pi_task,
    get_requester_gid_by_name,
    search_project_gid,
)
from integrations.asana.field_utils import (
    extract_pi_fields,
    handle_mask_type,
    map_package_size,
)
from routes.helpers.database_helpers import get_available_wafers

# from integrations.asana.custom_fields import add_custom_field
from routes.helpers.shipment_service import (
    create_shipment_task,
    get_available_locations,
    is_valid_status_transition,
    update_shipment_status,
)

# Create a Blueprint for shipment routes
shipment_bp = Blueprint("shipment", __name__, url_prefix="/shipment")


@shipment_bp.route("/api/shipments", methods=["POST"])
def create_shipment():
    """Create a new shipment task in Asana from form submission"""
    try:
        # Log request info for debugging
        current_app.logger.info(f"Request method: {request.method}")
        current_app.logger.info(f"Content type: {request.content_type}")
        current_app.logger.info(f"Is JSON? {request.is_json}")
        current_app.logger.info(f"Request path: {request.path}")

        # Get JSON data from request
        data = request.get_json()
        current_app.logger.info(f"Received shipment data: {data}")

        # Call the service function to create shipment task
        success, message, task_id = create_shipment_task(data)

        # Prepare the response
        if success:
            return jsonify({"success": True, "message": message, "task_id": task_id})
        else:
            return jsonify({"success": False, "message": message}), 500

    except Exception as e:
        current_app.logger.error(f"Error in create_shipment: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return (
            jsonify(
                {"success": False, "message": f"Error creating shipment task: {str(e)}"}
            ),
            500,
        )


@shipment_bp.route("/api/asana/modify", methods=["POST"])
def modify_asana_task():
    """Update an existing Asana task with form data"""
    try:
        data = request.get_json()
        asana_url = data.get("asana_url")
        if not asana_url:
            return jsonify({"success": False, "message": "Asana URL is required"}), 400

        # Extract task GID from URL
        task_gid = extract_task_gid(asana_url)
        if not task_gid:
            return (
                jsonify({"success": False, "message": "Invalid Asana URL format"}),
                400,
            )

        # Configure Asana client
        api_client = get_asana_client()
        tasks_api_instance = asana.TasksApi(api_client)

        # First check if the task exists and we have access
        try:
            task_details = tasks_api_instance.get_task(task_gid, opts=OPTS)
            task_details_data = (
                task_details.to_dict()
                if hasattr(task_details, "to_dict")
                else task_details
            )
        except Exception as e:
            return (
                jsonify(
                    {
                        "success": False,
                        "message": f"Task not found or access denied: {str(e)}",
                    }
                ),
                404,
            )

        # Get the form data to update the task
        form_data = data.get("form_data", {})
        if not form_data:
            return jsonify({"success": False, "message": "No form data provided"}), 400

        # Get LGT lot IDs from the input field (comma-separated)
        lot_ids_raw = form_data.get("lot_ids", "")
        lgt_lot_ids = []
        if isinstance(lot_ids_raw, str) and lot_ids_raw.strip():
            lgt_lot_ids = [lot.strip() for lot in lot_ids_raw.split(",") if lot.strip()]

        current_app.logger.info(f"Processing LGT lot IDs: {lgt_lot_ids}")

        # Build field map for easier access
        field_map = {}
        for field in task_details_data.get("custom_fields", []):
            if isinstance(field, dict) and "name" in field:
                field_name = field.get("name", "").strip()
                field_map[field_name] = field

        # Create initial task update with basic information
        body_updated = {
            "data": {
                "name": f"Additional-shipment: {form_data.get('title')}",
            }
        }

        # Add start/due dates if available
        if form_data.get("shipment_date"):
            try:
                formatted_date = datetime.strptime(
                    form_data.get("shipment_date"), "%Y-%m-%d"
                ).strftime("%Y-%m-%d")
                body_updated["data"]["start_on"] = formatted_date
                body_updated["data"]["due_on"] = formatted_date
            except Exception as e:
                current_app.logger.error(f"Error formatting date: {str(e)}")

        # Add requester if available
        if form_data.get("contact_person"):
            try:
                requester_gid = get_requester_gid_by_name(
                    api_client, form_data["contact_person"]
                )
                if requester_gid:
                    body_updated["data"]["followers"] = [requester_gid]
            except Exception as e:
                current_app.logger.error(f"Error getting requester GID: {str(e)}")

        # Build custom fields update
        custom_fields = {}

        # LGT Lot ID
        lgt_lot_field = field_map.get("LGT Lot ID")
        if lgt_lot_field and isinstance(lgt_lot_field, dict) and "gid" in lgt_lot_field:
            custom_fields[lgt_lot_field["gid"]] = ", ".join(lgt_lot_ids)

        # Process text fields
        text_fields = {
            "Shipment task title": form_data.get("title", ""),
            "Ligentec label title": form_data.get("label_title", ""),
            "Wafers IDs": ", ".join(form_data.get("wafer_ids", [])),
            "Number of Wafers": form_data.get("number_of_wafers", ""),
            "XFAB purchase order": form_data.get("xfab_po", ""),
            "XFAB Device ID": form_data.get("xfab_device_id", ""),
            "Project ID": form_data.get("project_id", ""),
            "Contact person": form_data.get("contact_person", ""),
            "Email (delivery contact)": form_data.get("email", ""),
            "Shipping address": form_data.get("address", ""),
            "Telephone number (delivery contact)": form_data.get("telephone", ""),
            "Shipment comments": form_data.get("comments", ""),
            "Eiger Number": form_data.get("eiger_number", ""),
            "Tapeout (Eiger)": form_data.get("tapeout", ""),
            "Vendor Lot": form_data.get("vendor_lot", ""),
            "Customer Lot (Eiger)": form_data.get("customer_lot", ""),
            "TOX Target nm": form_data.get("tox_target_sin", ""),
            "SiN Tube Position (1-6)": form_data.get("sin_tube_position", ""),
        }

        for field_name, value in text_fields.items():
            field = field_map.get(field_name)
            if field and isinstance(field, dict) and "gid" in field and value:
                custom_fields[field["gid"]] = str(value)
                current_app.logger.info(f"Setting text field {field_name}: {value}")

        # Process standard Yes/No boolean fields
        yes_no_fields = {
            "Need Reviewing?": form_data.get("need_reviewing", False),
            "label-free shipment ?": form_data.get("label_free", False),
            "Keep Cassette closed": form_data.get("keep_cassette_closed", False),
        }

        for field_name, is_true in yes_no_fields.items():
            field = field_map.get(field_name)
            if (
                field
                and isinstance(field, dict)
                and "gid" in field
                and "enum_options" in field
            ):
                options = field.get("enum_options", [])

                yes_option = next(
                    (
                        opt
                        for opt in options
                        if isinstance(opt, dict) and opt.get("name") == "Yes"
                    ),
                    None,
                )
                no_option = next(
                    (
                        opt
                        for opt in options
                        if isinstance(opt, dict) and opt.get("name") == "No"
                    ),
                    None,
                )

                if is_true and yes_option and isinstance(yes_option, dict):
                    custom_fields[field["gid"]] = yes_option.get("gid")
                    current_app.logger.info(f"Setting {field_name} to Yes")
                elif not is_true and no_option and isinstance(no_option, dict):
                    custom_fields[field["gid"]] = no_option.get("gid")
                    current_app.logger.info(f"Setting {field_name} to No")

        # Process TRUE/FALSE boolean fields
        true_false_fields = {
            "RIB ?": form_data.get("rib", False),
            "HEATERS?": form_data.get("heater", False),
            "Undercuts?": form_data.get("undercut", False),
        }

        for field_name, is_true in true_false_fields.items():
            field = field_map.get(field_name)
            if field and "gid" in field and field.get("enum_options"):
                options = field.get("enum_options", [])

                true_option = next(
                    (opt for opt in options if opt.get("name") == "TRUE"), None
                )
                false_option = next(
                    (opt for opt in options if opt.get("name") == "FALSE"), None
                )

                # Convert string value to boolean if needed
                if isinstance(is_true, str):
                    is_true = is_true.lower() in ["true", "yes", "1", "on"]

                if is_true and true_option:
                    custom_fields[field["gid"]] = true_option.get("gid")
                    current_app.logger.info(f"Setting {field_name} to TRUE")
                elif not is_true and false_option:
                    custom_fields[field["gid"]] = false_option.get("gid")
                    current_app.logger.info(f"Setting {field_name} to FALSE")

        # Handle Priority field
        priority_field = field_map.get("Priority")
        if (
            priority_field
            and "gid" in priority_field
            and priority_field.get("enum_options")
        ):
            priority = form_data.get("priority", "low").capitalize()
            priority_option = next(
                (
                    opt
                    for opt in priority_field.get("enum_options", [])
                    if opt.get("name") == priority
                ),
                None,
            )
            if priority_option:
                custom_fields[priority_field["gid"]] = priority_option.get("gid")
                current_app.logger.info(f"Setting Priority to {priority}")

        # Handle Wafer choice type field
        wafer_choice_field = field_map.get("Wafer choice type")
        if (
            wafer_choice_field
            and "gid" in wafer_choice_field
            and wafer_choice_field.get("enum_options")
        ):
            wafer_choice_value = form_data.get("wafer_choice")
            if wafer_choice_value:
                # Determine display value based on input
                display_value = (
                    "Not Random" if "not" in wafer_choice_value.lower() else "Random"
                )

                if wafer_choice_field["type"] == "text":
                    custom_fields[wafer_choice_field["gid"]] = display_value
                elif wafer_choice_field["type"] == "enum":
                    options = wafer_choice_field.get("enum_options", [])
                    option = next(
                        (opt for opt in options if opt.get("name") == display_value),
                        None,
                    )
                    if option:
                        custom_fields[wafer_choice_field["gid"]] = option.get("gid")

        # Handle Mask field
        mask_field = field_map.get("Mask")
        if mask_field and "gid" in mask_field:
            mask_value = form_data.get("mask")
            if mask_value:
                display_value = handle_mask_type(mask_value)

                if mask_field["type"] == "text":
                    custom_fields[mask_field["gid"]] = display_value
                elif mask_field["type"] == "enum" and mask_field.get("enum_options"):
                    options = mask_field.get("enum_options", [])
                    option = next(
                        (opt for opt in options if opt.get("name") == display_value),
                        None,
                    )
                    if option:
                        custom_fields[mask_field["gid"]] = option.get("gid")

        # Handle Package size field
        package_field = field_map.get("Package size & weight (200mm wafers)")
        if (
            package_field
            and "gid" in package_field
            and package_field.get("enum_options")
        ):
            package_value = form_data.get("parcel_size")
            if package_value:
                # Map the package size value to Asana's expected format
                display_value = map_package_size(package_value)

                if display_value:
                    options = package_field.get("enum_options", [])
                    option = next(
                        (opt for opt in options if opt.get("name") == display_value),
                        None,
                    )
                    if option:
                        custom_fields[package_field["gid"]] = option.get("gid")

        # Handle Type of shipment field
        shipment_type_field = field_map.get("Type of shipment")
        if (
            shipment_type_field
            and "gid" in shipment_type_field
            and shipment_type_field.get("enum_options")
        ):
            options = shipment_type_field.get("enum_options", [])

            # Default to "ePO, Projects, Samples, RMA, R&D, External service"
            default_option = next(
                (opt for opt in options if "ePO" in opt.get("name")), None
            )

            if default_option:
                custom_fields[shipment_type_field["gid"]] = default_option.get("gid")

        # Handle Initial start date and Initial due date fields
        if form_data.get("shipment_date"):
            formatted_date_iso = datetime.strptime(
                form_data.get("shipment_date"), "%Y-%m-%d"
            ).strftime("%Y-%m-%dT%H:%M:%SZ")

            # Initial start date
            initial_start_date_field = field_map.get("Initial start date")
            if initial_start_date_field and "gid" in initial_start_date_field:
                custom_fields[initial_start_date_field["gid"]] = {
                    "date": formatted_date_iso
                }

            # Initial due date
            initial_due_date_field = field_map.get("Initial due date")
            if initial_due_date_field and "gid" in initial_due_date_field:
                custom_fields[initial_due_date_field["gid"]] = {
                    "date": formatted_date_iso
                }

        # Update the task with custom fields
        if custom_fields:
            body_updated["data"]["custom_fields"] = custom_fields
            try:
                tasks_api_instance.update_task(body_updated, task_gid, OPTS)
            except Exception as e:
                current_app.logger.error(
                    f"Error updating task with custom fields: {str(e)}"
                )
                return jsonify(
                    {
                        "success": True,
                        "message": (
                            "Task Updated but some custom fields "
                            "could not be updated"
                        ),
                        "task_id": task_gid,
                    }
                )

        return jsonify(
            {
                "success": True,
                "message": "Asana task updated successfully",
                "task_id": task_gid,
            }
        )

    except Exception as e:
        current_app.logger.error(f"Error in modify_asana_task: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return (
            jsonify(
                {"success": False, "message": f"Error updating Asana task: {str(e)}"}
            ),
            500,
        )


@shipment_bp.route("/api/available-locations", methods=["GET"])
def get_locations():
    """Get all shipping locations"""
    success, message, locations = get_available_locations()

    if success:
        return jsonify({"success": True, "data": locations})
    else:
        return jsonify({"success": False, "message": message}), 500


@shipment_bp.route("/api/available-wafers", methods=["GET"])
def available_wafers():
    """Get available wafers for shipment with enhanced filtering and pagination"""
    try:
        # Get filter parameters
        filters = {
            "search": request.args.get("search", ""),
            "lot_ids": request.args.getlist("lot_ids[]"),
            "wafer_ids": request.args.getlist("wafer_ids[]"),
            "cassette_id": request.args.get("cassette_id"),
            "location_id": request.args.get("location_id"),
            "date_from": request.args.get("date_from"),
            "date_to": request.args.get("date_to"),
            "sort_by": request.args.get("sort_by", "lot_id"),
            "sort_order": request.args.get("sort_order", "asc"),
        }

        # Get pagination parameters
        pagination = {
            "page": request.args.get("page", 1, type=int),
            "per_page": request.args.get("per_page", 10, type=int),
        }

        # Call service function
        success, message, result = get_available_wafers(filters, pagination)

        if success:
            return jsonify(
                {
                    "success": True,
                    "data": result["data"],
                    "pagination": result["pagination"],
                    "filters": result["filters"],
                }
            )
        else:
            return jsonify({"success": False, "message": message}), 500

    except Exception as e:
        current_app.logger.error(f"Error fetching available wafers: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return (
            jsonify(
                {
                    "success": False,
                    "message": f"Error fetching available wafers: {str(e)}",
                }
            ),
            500,
        )


@shipment_bp.route("/api/shipments/<shipment_id>", methods=["PUT"])
def update_shipment(shipment_id):
    """Update shipment details and handle status transitions"""
    try:
        data = request.get_json()

        with get_sqlalchemy_connection() as conn:
            # Check if shipment exists
            existing_shipment = conn.execute(
                select([Shipment]).where(Shipment.shipment_id == shipment_id)
            ).fetchone()

            if not existing_shipment:
                return jsonify({"success": False, "message": "Shipment not found"}), 404

            # Validate status transition if status is being updated
            if "status" in data:
                new_status = ShipmentStatus(data["status"])
                if not is_valid_status_transition(existing_shipment.status, new_status):
                    return (
                        jsonify(
                            {
                                "success": False,
                                "message": (
                                    f"Invalid status transition from "
                                    f"{existing_shipment.status} to {new_status}"
                                ),
                            }
                        ),
                        400,
                    )

            # Build update data
            update_data = {}
            valid_fields = {
                "tracking_number",
                "status",
                "destination_location_id",
                "asana_task_id",
                "label_path",
                "packing_slip_path",
                "ship_date",
                "delivery_date",
            }

            for field in valid_fields:
                if field in data:
                    update_data[field] = data[field]

            # Update metadata if provided
            if "metadata" in data:
                current_metadata = dict(existing_shipment.shipment_metadata or {})
                current_metadata.update(data["metadata"])
                update_data["shipment_metadata"] = current_metadata

            update_data["updated_at"] = datetime.now(timezone.utc)
            update_data["updated_by"] = data.get("updated_by", "system")

            # Perform update
            conn.execute(
                Shipment.__table__.update()
                .where(Shipment.shipment_id == shipment_id)
                .values(update_data)
            )

            # Handle wafer updates if provided
            if "wafer_ids" in data:
                # Remove existing associations
                conn.execute(
                    ShipmentWafer.__table__.delete().where(
                        ShipmentWafer.shipment_id == shipment_id
                    )
                )

                # Add new associations
                for wafer_id in data["wafer_ids"]:
                    new_shipment_wafer = ShipmentWafer(
                        shipment_id=shipment_id,
                        wafer_id=wafer_id,
                        original_location_id=existing_shipment.origin_location_id,
                        created_at=datetime.now(timezone.utc),
                        updated_at=datetime.now(timezone.utc),
                    )
                    conn.execute(
                        ShipmentWafer.__table__.insert().values(
                            new_shipment_wafer.__dict__
                        )
                    )

            return jsonify(
                {"success": True, "message": "Shipment updated successfully"}
            )

    except Exception as e:
        current_app.logger.error(f"Error in update_shipment: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return (
            jsonify(
                {"success": False, "message": f"Error updating shipment: {str(e)}"}
            ),
            500,
        )


@shipment_bp.route("/api/shipments/<shipment_id>/status", methods=["PUT"])
def update_shipment_status_route(shipment_id):
    """Update shipment status with proper validation"""
    try:
        data = request.get_json()
        new_status = data.get("status")
        updated_by = data.get("updated_by", "system")

        if not new_status:
            return jsonify({"success": False, "message": "New status is required"}), 400

        # Call service function to update status
        success, message = update_shipment_status(shipment_id, new_status, updated_by)

        if success:
            return jsonify({"success": True, "message": message})
        else:
            if "not found" in message.lower():
                status_code = 404
            elif "invalid" in message.lower():
                status_code = 400
            else:
                status_code = 500

            return jsonify({"success": False, "message": message}), status_code

    except Exception as e:
        current_app.logger.error(f"Error in update_shipment_status: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return (
            jsonify(
                {
                    "success": False,
                    "message": f"Error updating shipment status: {str(e)}",
                }
            ),
            500,
        )


@shipment_bp.route("/shipment_dashboard", methods=["GET"])
def shipment_dashboard():
    """Render the shipment dashboard page"""
    return render_template("shipment/dashboard.html")


@shipment_bp.route("/api/asana/check-auth", methods=["GET"])
def check_asana_auth():
    """Check Asana API authentication and permissions"""
    try:
        # Configure Asana client
        api_client = get_asana_client()
        users_api = asana.UsersApi(api_client)

        # Get current user info
        me = users_api.get_user("me")

        # Return success with user details
        return jsonify(
            {
                "success": True,
                "user": {
                    "name": me.name,
                    "email": me.email,
                    "workspace_id": me.workspaces[0].gid if me.workspaces else None,
                },
            }
        )

    except Exception as e:
        return (
            jsonify(
                {
                    "success": False,
                    "message": f"Failed to authenticate with Asana API: {str(e)}",
                }
            ),
            401,
        )


def extract_task_gid(asana_url: str) -> Optional[str]:
    """Extract Asana task GID from URL"""
    if not asana_url:
        return None

    # Handle direct GID input (just numbers)
    if asana_url.isdigit():
        return asana_url

    # Common Asana URL patterns
    patterns = [
        # Standard task URL pattern
        r"asana\.com/\d+/\d+/(\d+)(?:/[a-z])?",
        # Alternative URL format with project
        r"app\.asana\.com/\d+/\d+/\d+/(\d+)(?:/[a-z])?",
        # Newer Asana format with more path segments
        r"app\.asana\.com/\d+/\d+/\d+/\d+/(\d+)",
        # Even newer Asana format with more path segments
        r"app\.asana\.com/\d+/\d+/\d+/\d+/\d+/(\d+)",
        # Task in URL format
        r"/task/(\d+)",
        # Task at end of URL
        r"/(\d+)\?",
        r"/(\d+)$",
        # Project task format
        r"/project/\d+/task/(\d+)",
    ]

    for pattern in patterns:
        match = re.search(pattern, asana_url)
        if match:
            return match.group(1)

    # If still no match, check if the URL contains a number sequence of at least 8 digits
    long_number_match = re.search(r"(\d{8,})", asana_url)
    if long_number_match:
        return long_number_match.group(1)

    return None


@shipment_bp.route("/api/locations/<location_id>", methods=["GET"])
def get_location_details(location_id):
    """Get detailed information about a specific location"""
    try:
        with get_db_cursor() as cursor:
            query = """
                SELECT
                    location_id,
                    label,
                    address,
                    telephone,
                    email,
                    contact_person
                FROM locations
                WHERE location_id = %s
            """
            cursor.execute(query, [location_id])
            location = cursor.fetchone()

            if not location:
                return jsonify({"success": False, "message": "Location not found"}), 404

            # Add destination_label from the label field
            location_data = dict(location)
            location_data["destination_label"] = location["label"]

            return jsonify({"success": True, "data": location_data})

    except Exception as e:
        current_app.logger.error(f"Error fetching location details: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return (
            jsonify(
                {
                    "success": False,
                    "message": f"Error fetching location details: {str(e)}",
                }
            ),
            500,
        )


@shipment_bp.route("/api/asana/extract", methods=["POST"])
def extract_asana_task():
    """Extract data from Asana task URL"""
    try:
        data = request.get_json()
        asana_url = data.get("asana_url")
        skip_pi_lookup = data.get(
            "skip_pi_lookup", False
        )  # Optional flag to skip PI lookup

        if not asana_url:
            return jsonify({"success": False, "message": "Asana URL is required"}), 400

        # Extract task GID from URL
        task_gid = extract_task_gid(asana_url)
        if not task_gid:
            return (
                jsonify({"success": False, "message": "Invalid Asana URL format"}),
                400,
            )

        # Configure Asana client
        api_client = get_asana_client()
        tasks_api_instance = asana.TasksApi(api_client)

        # Get task details
        try:
            task_details = tasks_api_instance.get_task(task_gid, opts=OPTS)
            task_details_data = (
                task_details.to_dict()
                if hasattr(task_details, "to_dict")
                else task_details
            )
        except Exception as e:
            current_app.logger.error(f"Error fetching Asana task {task_gid}: {str(e)}")
            return (
                jsonify(
                    {
                        "success": False,
                        "message": f"Task not found or access denied: {str(e)}",
                    }
                ),
                404,
            )

        # Extract form data from task with safe PI task lookup
        form_data = extract_data_from_task_safe(task_details_data, skip_pi_lookup)

        return jsonify(
            {
                "success": True,
                "message": "Asana task data extracted successfully",
                "data": form_data,
            }
        )

    except Exception as e:
        current_app.logger.error(f"Error in extract_asana_task: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return (
            jsonify(
                {
                    "success": False,
                    "message": f"Error extracting Asana task data: {str(e)}",
                }
            ),
            500,
        )


@shipment_bp.route("/api/asana/upload", methods=["POST"])
def upload_from_asana():
    """Upload and process data from an Asana task URL"""
    try:
        data = request.get_json()
        asana_url = data.get("asana_url")
        debug_mode = data.get("debug_mode", False)
        skip_pi_lookup = data.get(
            "skip_pi_lookup", False
        )  # Optional flag to skip PI lookup

        if not asana_url:
            return jsonify({"success": False, "message": "Asana URL is required"}), 400

        # Extract task GID from URL
        task_gid = extract_task_gid(asana_url)
        if not task_gid:
            return (
                jsonify({"success": False, "message": "Invalid Asana URL format"}),
                400,
            )

        # Configure Asana client
        api_client = get_asana_client()
        tasks_api_instance = asana.TasksApi(api_client)

        # Get task details
        try:
            task_details = tasks_api_instance.get_task(task_gid, opts=OPTS)
            task_details_data = (
                task_details.to_dict()
                if hasattr(task_details, "to_dict")
                else task_details
            )
        except Exception as e:
            if debug_mode:
                current_app.logger.error(f"Debug - Asana API error: {str(e)}")
            return (
                jsonify(
                    {
                        "success": False,
                        "message": f"Task not found or access denied: {str(e)}",
                    }
                ),
                404,
            )

        # Extract form data from task with safe PI task lookup
        form_data = extract_data_from_task_safe(task_details_data, skip_pi_lookup)

        if debug_mode:
            current_app.logger.info(f"Debug - Extracted data: {form_data}")

        return jsonify(
            {
                "success": True,
                "message": "Asana task data uploaded successfully",
                "data": form_data,
            }
        )

    except Exception as e:
        current_app.logger.error(f"Error in upload_from_asana: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return (
            jsonify(
                {"success": False, "message": f"Error uploading from Asana: {str(e)}"}
            ),
            500,
        )


def process_custom_field(field, form_data):
    """Process a single custom field and update form_data"""
    field_name = field.get("name", "")
    field_type = field.get("type", "")

    if field_type == "text":
        return process_text_field(field_name, field.get("text_value", ""), form_data)
    elif field_type == "enum" and field.get("enum_value"):
        return process_enum_field(
            field_name, field.get("enum_value", {}).get("name", ""), form_data
        )

    return form_data


def process_text_field(field_name, field_value, form_data):
    """Process text type custom fields"""
    field_value = field_value or ""

    field_mappings = {
        "Wafers IDs": lambda: {
            "wafer_ids": [w.strip() for w in field_value.split(",") if w.strip()]
        },
        "LGT Lot ID": lambda: {"lot_ids": field_value},
        "Number of Wafers": lambda: {"number_of_wafers": field_value},
        "Contact person": lambda: {"contact_person": field_value},
        "Email (delivery contact)": lambda: {"email": field_value},
        "Shipping address": lambda: {"address": field_value},
        "Telephone number (delivery contact)": lambda: {"telephone": field_value},
        "Ligentec label title": lambda: {"label_title": field_value},
        "Shipment comments": lambda: {"comments": field_value},
        "XFAB purchase order": lambda: {"xfab_po": field_value},
        "XFAB Device ID": lambda: {"xfab_device_id": field_value},
        "Project ID": lambda: {"project_id": field_value},
        "Eiger Number": lambda: {"eiger_number": field_value},
        "Tapeout (Eiger)": lambda: {"tapeout": field_value},
        "Vendor Lot": lambda: {"vendor_lot": field_value},
        "Customer Lot (Eiger)": lambda: {"customer_lot": field_value},
        "TOX Target nm": lambda: {"tox_target_sin": field_value},
        "SiN Tube Position (1-6)": lambda: {"sin_tube_position": field_value},
    }

    if field_name in field_mappings:
        form_data.update(field_mappings[field_name]())

    return form_data


def process_enum_field(field_name, enum_value, form_data):
    """Process enum type custom fields"""
    field_mappings = {
        "Priority": lambda: {"priority": enum_value.lower()},
        "Need Reviewing?": lambda: {"need_reviewing": enum_value == "Yes"},
        "label-free shipment ?": lambda: {"label_free": enum_value == "Yes"},
        "Keep Cassette closed": lambda: {"keep_cassette_closed": enum_value == "Yes"},
        "Wafer choice type": lambda: {"wafer_choice": enum_value},
        "RIB ?": lambda: {"rib": enum_value == "TRUE"},
        "HEATERS?": lambda: {"heater": enum_value == "TRUE"},
        "Undercuts?": lambda: {"undercut": enum_value == "TRUE"},
        "Mask": lambda: {"mask": enum_value},
    }

    if field_name in field_mappings:
        form_data.update(field_mappings[field_name]())
    elif field_name == "Package size & weight (200mm wafers)":
        if "40x40x35" in enum_value:
            form_data["parcel_size"] = "40x40x35"
        elif "40x80x35" in enum_value:
            form_data["parcel_size"] = "40x80x35"

    return form_data


def process_pi_task_lookup(form_data):
    """Handle PI task lookup and data extraction"""

    def timeout_handler(*_):
        raise TimeoutError("PI task lookup timed out")

    try:
        import signal

        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(5)

        lot_ids = [
            lot.strip() for lot in form_data["lot_ids"].split(",") if lot.strip()
        ]
        if lot_ids:
            api_client = get_asana_client()
            project_gids = search_project_gid(api_client, lot_ids)

            for gid in project_gids:
                if gid:
                    pi_task = get_pi_task(api_client, gid, OPTS)
                    if pi_task:
                        pi_data = extract_pi_fields(
                            pi_task.to_dict()
                            if hasattr(pi_task, "to_dict")
                            else pi_task
                        )
                        form_data.update({k: v for k, v in pi_data.items() if v})
                        break
    finally:
        signal.alarm(0)

    return form_data


def extract_data_from_task_safe(task_data, skip_pi_lookup=False):
    """Extract structured data from an Asana task with safely handling PI task lookup"""
    if not task_data:
        return {}

    # Initialize form data with defaults
    form_data = {
        "title": "",
        "priority": "medium",
        "need_reviewing": False,
        "label_free": False,
        "keep_cassette_closed": False,
        "wafer_choice": "Random",
        "number_of_wafers": "",
        "wafer_ids": [],
        "lot_ids": "",
        "contact_person": "",
        "email": "",
        "address": "",
        "telephone": "",
        "destination_label": "",
        "shipment_date": "",
        "comments": "",
        "xfab_po": "",
        "xfab_device_id": "",
        "project_id": "",
        "eiger_number": "",
        "tapeout": "",
        "vendor_lot": "",
        "customer_lot": "",
        "rib": False,
        "heater": False,
        "undercut": False,
        "tox_target_sin": "",
        "sin_tube_position": "",
        "mask": "E-beam",
        "corridor": "",
        "lot_project": "",
        "lot_reservation": "",
        "account_manager": "",
        "sales_order": "",
        "customer_id": "",
    }

    # Extract basic task data
    form_data["title"] = task_data.get("name", "").replace("Additional-shipment: ", "")
    if task_data.get("due_on"):
        form_data["shipment_date"] = task_data.get("due_on")

    # Process custom fields
    for field in task_data.get("custom_fields", []):
        try:
            form_data = process_custom_field(field, form_data)
        except Exception as e:
            current_app.logger.error(
                f"Error processing field {field.get('name', '')}: {str(e)}"
            )

    # Process notes
    notes = task_data.get("notes", "")
    if notes and not form_data["destination_label"]:
        for line in notes.split("\n"):
            line = line.strip()
            if ":" in line and len(line) < 100:
                form_data["destination_label"] = line
                break

    # Handle PI task lookup
    if not skip_pi_lookup and form_data.get("lot_ids"):
        try:
            form_data = process_pi_task_lookup(form_data)
        except (TimeoutError, Exception) as e:
            current_app.logger.error(
                f"Error or timeout during PI task lookup: {str(e)}"
            )

    return form_data
