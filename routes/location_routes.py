import csv
import logging
import re
import traceback
from datetime import datetime
from functools import wraps
from io import BytesIO, String<PERSON>
from typing import Dict, List, Optional

import asana

# from flask_login import login_required
import phonenumbers
from email_validator import EmailNotValidError, validate_email
from flask import Blueprint, current_app, jsonify, request, send_file, session
from flask_wtf.csrf import CSRFProtect, ValidationError, validate_csrf
from sqlalchemy import text

from core.auth.auth import login_required
from database.db_config import get_db_cursor, get_sqlalchemy_connection
from database.db_helpers import require_database

logger = logging.getLogger(__name__)

# Create blueprint and csrf protection instance
location_bp = Blueprint("location", __name__)
csrf = CSRFProtect()


def csrf_protected(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            # Check for CSRF token in headers first
            token = request.headers.get("X-CSRFToken")

            # If not in headers, try request body
            if not token and request.is_json:
                token = request.json.get("csrf_token")

            if not token:
                current_app.logger.error("No CSRF token found")
                return jsonify({"success": False, "message": "CSRF token missing"}), 400

            # Validate the token
            try:
                validate_csrf(token)
            except ValidationError:
                current_app.logger.error("Invalid CSRF token")
                return jsonify({"success": False, "message": "Invalid CSRF token"}), 400

            return f(*args, **kwargs)

        except Exception as e:
            current_app.logger.error(f"CSRF validation error: {str(e)}")
            return jsonify({"success": False, "message": "CSRF validation failed"}), 400

    return decorated_function


@location_bp.route("/api/locations", methods=["GET"])
@location_bp.route("/api/locations/list", methods=["GET"])
@require_database()
@csrf.exempt  # Exempt from CSRF for this read-only endpoint
def get_locations():
    try:
        search_term = request.args.get("search", "").strip()

        with get_db_cursor() as cursor:
            query = """
                SELECT DISTINCT
                    location_id,
                    label,
                    address,
                    contact_person
                FROM locations
                WHERE 1=1
            """

            params = {}
            if search_term:
                # Add ILIKE for case-insensitive partial matching
                query += """
                    AND (
                        label ILIKE %(search)s OR
                        address ILIKE %(search)s OR
                        contact_person ILIKE %(search)s OR
                        email ILIKE %(search)s OR
                        telephone ILIKE %(search)s
                    )"""
                params["search"] = (
                    f"%{search_term}%"  # Add wildcards for partial matching
                )

            query += " ORDER BY label ASC"

            cursor.execute(query, params)
            locations = [
                {"location_id": row["location_id"], "label": row["label"]}
                for row in cursor.fetchall()
            ]

            return jsonify(
                {"success": True, "locations": locations, "total": len(locations)}
            )

    except Exception as e:
        current_app.logger.error(f"Error fetching locations: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500


@location_bp.route("/api/locations/export", methods=["GET"])
@require_database()
def export_locations():
    try:
        with get_sqlalchemy_connection() as conn:
            query = text(
                """
                SELECT
                    location_id,
                    label,
                    address,
                    email,
                    telephone,
                    contact_person,
                    updated_at,
                    updated_by
                FROM locations
                ORDER BY label ASC
            """
            )

            result = conn.execute(query)
            locations = result.fetchall()

            # Create CSV in memory using StringIO first
            si = StringIO()
            cw = csv.writer(si)

            # Write headers
            cw.writerow(
                [
                    "Location ID",
                    "Label",
                    "Address",
                    "Email",
                    "Telephone",
                    "Contact Person",
                    "Last Updated",
                    "Updated By",
                ]
            )

            # Write data
            for loc in locations:
                cw.writerow(
                    [
                        loc.location_id,
                        loc.label,
                        loc.address,
                        loc.email,
                        loc.telephone,
                        loc.contact_person,
                        (
                            loc.updated_at.strftime("%Y-%m-%d %H:%M:%S")
                            if loc.updated_at
                            else ""
                        ),
                        loc.updated_by,
                    ]
                )

            # Get the CSV data and convert to bytes
            output = si.getvalue().encode("utf-8")
            si.close()

            # Create BytesIO object
            mem = BytesIO()
            mem.write(output)
            mem.seek(0)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"locations_export_{timestamp}.csv"

            return send_file(
                mem, mimetype="text/csv", as_attachment=True, download_name=filename
            )

    except Exception as e:
        current_app.logger.error(f"Error exporting locations: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500


@location_bp.route("/api/location/view/<location_id>", methods=["GET"])
@csrf.exempt
@require_database()
def view_location(location_id):
    try:
        with get_sqlalchemy_connection() as conn:
            query = text(
                """
                SELECT
                    location_id,
                    label,
                    address,
                    email,
                    telephone,
                    contact_person,
                    updated_at,
                    updated_by
                FROM locations
                WHERE location_id = :location_id
            """
            )

            result = conn.execute(query, {"location_id": location_id})
            location = result.first()

            if not location:
                return jsonify({"success": False, "message": "Location not found"}), 404

            location_data = {
                "location_id": location.location_id,
                "label": location.label,
                "address": location.address,
                "email": location.email,
                "telephone": location.telephone,
                "contact_person": location.contact_person,
                "updated_at": (
                    location.updated_at.strftime("%Y-%m-%d %H:%M:%S")
                    if location.updated_at
                    else None
                ),
                "updated_by": location.updated_by,
            }

            return jsonify({"success": True, "location": location_data})

    except Exception as e:
        current_app.logger.error(f"Error viewing location: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500


@location_bp.route("/api/location/add", methods=["POST"])
@csrf_protected
@require_database()
def add_location():
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "message": "No data provided"}), 400

        # Validate data
        errors = validate_location_data(data)
        if errors:
            return (
                jsonify(
                    {"success": False, "message": "Validation failed", "errors": errors}
                ),
                400,
            )

        with get_sqlalchemy_connection() as conn:
            # Check if location already exists
            check_query = text(
                """
                SELECT location_id FROM locations
                WHERE LOWER(label) = LOWER(:label)"""
            )

            result = conn.execute(check_query, {"label": data.get("label")})
            if result.first():
                return (
                    jsonify(
                        {
                            "success": False,
                            "message": "Location with this label already exists",
                        }
                    ),
                    400,
                )

            insert_query = text(
                """
                INSERT INTO locations (
                    location_id,
                    label,
                    address,
                    email,
                    telephone,
                    contact_person,
                    updated_at,
                    updated_by
                )
                VALUES (
                    :location_id,
                    :label,
                    :address,
                    :email,
                    :telephone,
                    :contact_person,
                    NOW(),
                    :updated_by
                )
                RETURNING location_id
            """
            )

            result = conn.execute(
                insert_query,
                {
                    "location_id": data.get("location_id"),
                    "label": data.get("label"),
                    "address": data.get("address"),
                    "email": data.get("email"),
                    "telephone": data.get("telephone"),
                    "contact_person": data.get("contact_person"),
                    "updated_by": data.get("updated_by", "system"),
                },
            )

            location_id = result.scalar()
            conn.commit()

            return jsonify(
                {
                    "success": True,
                    "message": "Location added successfully",
                    "id": location_id,
                }
            )

    except Exception as e:
        current_app.logger.error(f"Error adding location: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500


@location_bp.route("/api/location/modify", methods=["PUT"])
@csrf_protected
@require_database()
def modify_location():
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "message": "No data provided"}), 400

        # Validate data
        errors = validate_location_data(data)
        if errors:
            return (
                jsonify(
                    {"success": False, "message": "Validation failed", "errors": errors}
                ),
                400,
            )

        with get_sqlalchemy_connection() as conn:
            query = text(
                """
                UPDATE locations
                SET
                    address = :address,
                    email = :email,
                    telephone = :telephone,
                    contact_person = :contact_person,
                    updated_at = NOW(),
                    updated_by = :updated_by
                WHERE location_id = :location_id
                RETURNING location_id
            """
            )

            result = conn.execute(
                query,
                {
                    "location_id": data["location_id"],
                    "address": data["address"],
                    "email": data["email"],
                    "telephone": data["telephone"],
                    "contact_person": data.get("contact_person"),
                    "updated_by": data.get("updated_by", "system"),
                },
            )

            if result.rowcount == 0:
                return jsonify({"success": False, "message": "Location not found"}), 404

            conn.commit()
            return jsonify(
                {"success": True, "message": "Location updated successfully"}
            )

    except Exception as e:
        current_app.logger.error(f"Error modifying location: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500


def validate_location_data(data: Dict) -> List[str]:
    """Validate location data and return list of error messages."""
    errors = []

    # Validate email
    if data.get("email"):
        try:
            validate_email(data["email"])
        except EmailNotValidError as e:
            errors.append(str(e))

    # Validate phone
    if data.get("telephone"):
        try:
            parsed_number = phonenumbers.parse(data["telephone"], None)
            if not phonenumbers.is_valid_number(parsed_number):
                errors.append("Invalid phone number format")
        except Exception:
            errors.append("Invalid phone number format")

    # Required fields
    required_fields = {
        "label": "Location label",
        "address": "Address",
        "contact_person": "Contact person",
    }

    for field, name in required_fields.items():
        if not data.get(field):
            errors.append(f"{name} is required")

    return errors


@location_bp.route("/api/location/delete", methods=["DELETE"])
@csrf_protected
@require_database()
def delete_location():
    try:
        data = request.get_json()
        if not data or "location_id" not in data:
            return (
                jsonify({"success": False, "message": "Location ID not provided"}),
                400,
            )

        location_id = data["location_id"]
        current_app.logger.info(f"Attempting to delete location: {location_id}")

        with get_sqlalchemy_connection() as conn:
            # First check if location exists
            check_query = text(
                """
                SELECT location_id FROM locations
                WHERE location_id = :location_id
            """
            )
            result = conn.execute(check_query, {"location_id": location_id})
            if not result.first():
                return jsonify({"success": False, "message": "Location not found"}), 404

            # Then check if location has any wafers
            check_wafers_query = text(
                """
                SELECT COUNT(*) FROM wafer_inventory
                WHERE location_id = :location_id
            """
            )
            result = conn.execute(check_wafers_query, {"location_id": location_id})
            if result.scalar() > 0:
                return (
                    jsonify(
                        {
                            "success": False,
                            "message": "Cannot delete location that has wafers",
                        }
                    ),
                    400,
                )

            # Finally, delete the location
            delete_query = text(
                """
                DELETE FROM locations
                WHERE location_id = :location_id
            """
            )
            result = conn.execute(delete_query, {"location_id": location_id})
            conn.commit()

            return jsonify(
                {"success": True, "message": "Location deleted successfully"}
            )

    except Exception as e:
        current_app.logger.error(f"Error deleting location: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500


def extract_task_gid(url_task: str) -> Optional[str]:
    """
    Extract the task_gid from the Asana task URL.
    Handles multiple URL formats including:
    - https://app.asana.com/0/123456789/987654321
    - https://app.asana.com/0/123456789/987654321/f
    - https://app.asana.com/0/search/987654321
    - Direct task GID input (just numbers)
    """
    try:
        if not url_task:
            return None

        # Handle direct GID input (just numbers)
        if url_task.isdigit():
            return url_task

        # Remove trailing slashes and 'f' if present
        url_task = url_task.rstrip("/f").rstrip("/")

        # Try different regex patterns
        patterns = [
            # Standard Asana URL patterns
            r"asana\.com/\d+/\d+/(\d+)(?:/[a-z])?",
            r"app\.asana\.com/\d+/\d+/\d+/(\d+)(?:/[a-z])?",
            r"app\.asana\.com/\d+/\d+/\d+/\d+/(\d+)",
            r"app\.asana\.com/\d+/\d+/\d+/\d+/\d+/(\d+)",
            # Common URL path patterns
            r"/(\d+)$",  # Matches the last number in the URL
            r"/task/(\d+)",  # Matches task/number format
            r"/(\d+)/f$",  # Matches number/f format
            r"/(\d+)\?",  # Matches number followed by query parameters
            r"/project/\d+/task/(\d+)",  # Matches project/number/task/number format
        ]

        for pattern in patterns:
            match = re.search(pattern, url_task)
            if match:
                task_gid = match.group(1)
                # Validate that we got a reasonable task ID
                if task_gid and task_gid.isdigit():
                    return task_gid

        # If still no match, check if the URL contains a number sequence of at least 8 digits
        long_number_match = re.search(r"(\d{8,})", url_task)
        if long_number_match:
            return long_number_match.group(1)

        return None

    except Exception as e:
        current_app.logger.error(
            f"Error extracting task GID from URL '{url_task}': {str(e)}"
        )
        return None


def get_asana_task_info(task_gid: str) -> Optional[Dict[str, Optional[str]]]:
    """
    Retrieve task information from Asana using the API.
    """
    try:
        # Handle task_gid with colon (format like "1210206616318785:1")
        if task_gid and ":" in task_gid:
            task_gid = task_gid.split(":")[0]

        configuration = asana.Configuration()
        configuration.access_token = (
            "1/1204968822616977:ed9cf542d701d8a0809f0155bb7bd306"
        )
        api_client = asana.ApiClient(configuration)
        tasks_api_instance = asana.TasksApi(api_client)

        current_app.logger.info(f"Fetching task info for GID: {task_gid}")

        opts = {
            "opt_pretty": True,
            "opt_fields": "name,notes,due_on,completed,assignee,custom_fields",
        }
        task = tasks_api_instance.get_task(task_gid, opts=opts)
        task_data = task["data"] if isinstance(task, dict) and "data" in task else task

        current_app.logger.info(f"Retrieved raw task data: {task_data}")

        # Initialize task_info with base fields
        task_info = {
            "gid": task_gid,
            "Task Name": task_data.get("name", "N/A"),
            "Task Description": task_data.get("notes", "N/A"),
            "Due Date": str(task_data.get("due_on", "N/A")),
            "Completed": "Yes" if task_data.get("completed", False) else "No",
            "Assignee": task_data.get("assignee", {}).get("name", "N/A"),
        }

        # Process custom fields
        custom_fields = task_data.get("custom_fields", [])
        for field in custom_fields:
            field_name = field.get("name", "Unknown Field")
            if field.get("display_value") is not None:
                field_value = field["display_value"]
            elif field.get("text_value") is not None:
                field_value = field["text_value"]
            elif field.get("number_value") is not None:
                field_value = str(field["number_value"])
            elif field.get("enum_value"):
                field_value = field["enum_value"].get("name", "N/A")
            else:
                field_value = "N/A"

            task_info[field_name] = field_value

        current_app.logger.info(f"Processed task info: {task_info}")
        return task_info

    except Exception as e:
        current_app.logger.error(f"Error retrieving Asana task info: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return None


@location_bp.route("/api/get_task_info", methods=["GET"])
def get_task_info_endpoint():
    try:
        # Initialize asana_link variable with None to avoid UnboundLocalError
        asana_link = None

        # Check for task_gid parameter first (direct GID)
        task_gid = request.args.get("task_gid")

        # If no task_gid, try asana_link parameter
        if not task_gid:
            asana_link = request.args.get("asana_link")
            current_app.logger.info(f"Received Asana link: {asana_link}")

            if not asana_link:
                return (
                    jsonify(
                        {
                            "success": False,
                            "message": "No Asana link or task_gid provided",
                        }
                    ),
                    400,
                )

            task_gid = extract_task_gid(asana_link)
            if not task_gid:
                return (
                    jsonify({"success": False, "message": "Invalid Asana link format"}),
                    400,
                )

        current_app.logger.info(f"Using task_gid: {task_gid}")

        task_info = get_asana_task_info(task_gid)
        if not task_info:
            return (
                jsonify(
                    {"success": False, "message": "Failed to retrieve task information"}
                ),
                404,
            )

        # Map Asana fields to location data
        location_data = {
            # Use Ligentec label title instead of Customer ID
            "label": task_info.get("Ligentec label title", ""),
            # Exact field from Asana
            "address": task_info.get("Shipping address", ""),
            # Exact field from Asana
            "contact_person": task_info.get("Contact person", ""),
            # Updated field name
            "telephone": task_info.get("Telephone number (delivery contact)", ""),
            # Updated field name
            "email": task_info.get("Email (delivery contact)", ""),
            # Only include asana_link if it exists
            "asana_link": asana_link if asana_link else "",
        }

        current_app.logger.info(f"Mapped location data: {location_data}")

        return jsonify({"success": True, "task_info": location_data})

    except Exception as e:
        current_app.logger.error(f"Error getting task info: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({"success": False, "message": str(e)}), 500


@location_bp.route("/api/current_user", methods=["GET"])
@login_required
def get_current_user():
    """Get information about the current logged-in user."""
    try:
        # Access user information from the session
        if (
            "user_email" in session
            and "user_name" in session
            and "user_role" in session
        ):
            user_info = {
                "email": session["user_email"],
                "name": session["user_name"],
                "role": session["user_role"],
                "id": session.get("user_id"),
            }
            return jsonify({"success": True, "user": user_info})
        else:
            return jsonify({"success": False, "message": "User not authenticated"}), 401

    except Exception as e:
        current_app.logger.error(f"Error fetching current user: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500


# Initialize the blueprint with CSRF protection


def init_blueprint(app):
    csrf.init_app(app)
    app.register_blueprint(location_bp)
    app.logger.info("Location routes initialized")

    return location_bp
