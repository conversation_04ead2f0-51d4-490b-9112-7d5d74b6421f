"""
Location handling operations for wafer inventory management.
This module provides specific functions for managing locations in the database.
"""

import logging
import uuid
from datetime import datetime
from typing import Dict, Optional

from core.models.models import Location
from database.db_operations import get_session

# Configure logging
logger = logging.getLogger(__name__)


def get_all_locations():
    """Get all locations from the database"""
    try:
        with get_session() as session:
            locations = session.query(Location).all()
            return [
                {
                    "location_id": loc.location_id,
                    "label": loc.label,
                    "address": loc.address or "",
                    "email": loc.email or "",
                    "telephone": loc.telephone or "",
                    "contact_person": loc.contact_person or "",
                    "asana_link": loc.asana_link or "",
                }
                for loc in locations
            ]
    except Exception as e:
        logger.error(f"Error getting all locations: {str(e)}")
        return []


def get_available_shipping_locations():
    """Get locations that have wafers available for shipment (green locations)"""
    try:
        with get_session() as session:
            # These are the locations you mentioned as having "green" status
            available_locations = ["Ligentec France", "Xfab FR", "Ligentec FR Khalil"]

            locations = (
                session.query(Location)
                .filter(Location.label.in_(available_locations))
                .all()
            )

            return [
                {
                    "location_id": loc.location_id,
                    "label": loc.label,
                    "address": loc.address or "",
                    "email": loc.email or "",
                    "telephone": loc.telephone or "",
                    "contact_person": loc.contact_person or "",
                }
                for loc in locations
            ]
    except Exception as e:
        logger.error(f"Error getting available shipping locations: {str(e)}")
        return []


def get_location_by_id(location_id: str) -> Optional[Dict]:
    """Get location details by ID"""
    try:
        with get_session() as session:
            location = (
                session.query(Location)
                .filter(Location.location_id == location_id)
                .first()
            )

            if not location:
                return None

            return {
                "location_id": location.location_id,
                "label": location.label,
                "address": location.address or "",
                "email": location.email or "",
                "telephone": location.telephone or "",
                "contact_person": location.contact_person or "",
                "asana_link": location.asana_link or "",
            }
    except Exception as e:
        logger.error(f"Error getting location {location_id}: {str(e)}")
        return None


def get_location_by_label(label: str) -> Optional[Dict]:
    """Get location details by label"""
    try:
        with get_session() as session:
            location = session.query(Location).filter(Location.label == label).first()

            if not location:
                return None

            return {
                "location_id": location.location_id,
                "label": location.label,
                "address": location.address or "",
                "email": location.email or "",
                "telephone": location.telephone or "",
                "contact_person": location.contact_person or "",
                "asana_link": location.asana_link or "",
            }
    except Exception as e:
        logger.error(f"Error getting location with label {label}: {str(e)}")
        return None


def create_location(location_data: Dict) -> Optional[str]:
    """Create a new location in the database"""
    try:
        with get_session() as session:
            # Check if location with same label already exists
            existing = (
                session.query(Location)
                .filter(Location.label == location_data["label"])
                .first()
            )

            if existing:
                logger.warning(
                    f"Location with label '{location_data['label']}' already exists"
                )
                return None

            # Generate location ID if not provided
            location_id = location_data.get("location_id", str(uuid.uuid4()))

            # Create new location
            new_location = Location(
                location_id=location_id,
                label=location_data["label"],
                address=location_data.get("address", ""),
                email=location_data.get("email", ""),
                telephone=location_data.get("telephone", ""),
                contact_person=location_data.get("contact_person", ""),
                asana_link=location_data.get("asana_link", ""),
                updated_at=datetime.now(),
                updated_by=location_data.get("updated_by", "system"),
            )

            session.add(new_location)
            session.commit()

            return location_id
    except Exception as e:
        logger.error(f"Error creating location: {str(e)}")
        return None


def update_location(location_id: str, location_data: Dict) -> bool:
    """Update an existing location"""
    try:
        with get_session() as session:
            location = (
                session.query(Location)
                .filter(Location.location_id == location_id)
                .first()
            )

            if not location:
                logger.error(f"Location {location_id} not found")
                return False

            # Update fields
            if "label" in location_data:
                location.label = location_data["label"]
            if "address" in location_data:
                location.address = location_data["address"]
            if "email" in location_data:
                location.email = location_data["email"]
            if "telephone" in location_data:
                location.telephone = location_data["telephone"]
            if "contact_person" in location_data:
                location.contact_person = location_data["contact_person"]
            if "asana_link" in location_data:
                location.asana_link = location_data["asana_link"]

            location.updated_at = datetime.now()
            location.updated_by = location_data.get("updated_by", "system")

            session.commit()
            return True
    except Exception as e:
        logger.error(f"Error updating location {location_id}: {str(e)}")
        return False


def delete_location(location_id: str) -> bool:
    """Delete a location (if not referenced by any wafers)"""
    try:
        with get_session() as session:
            # Check if location is referenced by any wafers
            from core.models.models import WaferInventory

            used = (
                session.query(WaferInventory)
                .filter(WaferInventory.location_id == location_id)
                .first()
            )

            if used:
                logger.error(
                    f"Cannot delete location {location_id} - it is referenced by wafers"
                )
                return False

            # Delete location
            location = (
                session.query(Location)
                .filter(Location.location_id == location_id)
                .first()
            )

            if not location:
                logger.error(f"Location {location_id} not found")
                return False

            session.delete(location)
            session.commit()
            return True
    except Exception as e:
        logger.error(f"Error deleting location {location_id}: {str(e)}")
        return False


def get_sent_to_customer_location_id() -> Optional[str]:
    """Get the 'Sent to Customer' location ID"""
    try:
        with get_session() as session:
            location = (
                session.query(Location)
                .filter(Location.label == "Sent to Customer")
                .first()
            )

            if not location:
                logger.error("'Sent to Customer' location not found")
                return None

            return location.location_id
    except Exception as e:
        logger.error(f"Error getting 'Sent to Customer' location: {str(e)}")
        return None


def ensure_required_locations() -> bool:
    """Ensure that required locations exist in the database"""
    try:
        required_locations = [
            {
                "label": "Ligentec France",
                "address": "224 Boulevard John Kennedy, 91100 Corbeil-Essonnes, France",
            },
            {"label": "Xfab FR", "address": ""},
            {"label": "Ligentec FR Khalil", "address": ""},
            {"label": "Sent to Customer", "address": ""},
        ]

        with get_session() as session:
            created_count = 0

            for loc_data in required_locations:
                existing = (
                    session.query(Location)
                    .filter(Location.label == loc_data["label"])
                    .first()
                )

                if not existing:
                    # Create location
                    new_location = Location(
                        location_id=str(uuid.uuid4()),
                        label=loc_data["label"],
                        address=loc_data["address"],
                        updated_at=datetime.now(),
                        updated_by="system_init",
                    )

                    session.add(new_location)
                    created_count += 1

            if created_count > 0:
                session.commit()
                logger.info(f"Created {created_count} required locations")

            return True
    except Exception as e:
        logger.error(f"Error ensuring required locations: {str(e)}")
        return False
