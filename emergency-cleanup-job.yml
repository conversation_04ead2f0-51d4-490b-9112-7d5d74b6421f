# Emergency cleanup job - minimal version
# Copy this job definition to your .gitlab-ci.yml if you need the simplest possible version

auto-cleanup:
  stage: auto-cleanup
  image: alpine:latest
  before_script:
    - apk add openssh-client
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - ssh-keyscan -H $SSH_HOST >> ~/.ssh/known_hosts
  script:
    - echo "Starting cleanup..."
    - ssh ${SSH_OPTIONS} $SSH_URI "docker system prune -f"
    - ssh ${SSH_OPTIONS} $SSH_URI "docker volume prune -f"
    - echo "Cleanup done"
  only:
    - branches
  allow_failure: true