# Simple cleanup job for GitLab CI
# Minimal version for emergency disk space cleanup

auto-cleanup:
  stage: auto-cleanup
  image: alpine:latest
  before_script:
  - apk add --no-cache openssh-client
  - eval $(ssh-agent -s)
  - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
  - mkdir -p ~/.ssh
  - chmod 700 ~/.ssh
  - ssh-keyscan -H $SSH_HOST >> ~/.ssh/known_hosts
  script:
  - echo "Starting cleanup to prevent disk space issues..."
  - ssh ${SSH_OPTIONS} $SSH_URI "df -h"
  - ssh ${SSH_OPTIONS} $SSH_URI "docker system prune -f" || true
  - ssh ${SSH_OPTIONS} $SSH_URI "docker image prune -af --filter 'until=24h'" || true
  - ssh ${SSH_OPTIONS} $SSH_URI "docker volume prune -f" || true
  - echo "Cleanup completed"
  - ssh ${SSH_OPTIONS} $SSH_URI "df -h"
  rules:
  - if: $CI_PIPELINE_SOURCE == "merge_request_event"
  - if: $CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS
    when: never
  - if: $CI_COMMIT_BRANCH
  allow_failure: true
  timeout: 5m
