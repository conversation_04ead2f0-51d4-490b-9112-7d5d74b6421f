<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modal Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Modal responsive fixes for different zoom levels */
        #emailPreviewModal .modal-content {
            max-height: 95vh;
            min-height: 0;
        }

        #emailPreviewModal .flex-1.min-h-0 {
            /* Ensure the scrollable content area doesn't overflow */
            overflow-y: auto;
            max-height: calc(95vh - 200px); /* Account for header and footer */
        }

        /* Ensure buttons are always visible */
        @media (max-height: 700px) {
            #emailPreviewModal .modal-content {
                max-height: 98vh;
            }
            
            #emailPreviewModal .flex-1.min-h-0 {
                max-height: calc(98vh - 180px);
            }
            
            #emailBodyEditor {
                min-height: 200px !important;
                max-height: 250px !important;
            }
        }

        /* For very small screens or high zoom */
        @media (max-height: 600px) {
            #emailPreviewModal .modal-content {
                max-height: 99vh;
            }
            
            #emailPreviewModal .flex-1.min-h-0 {
                max-height: calc(99vh - 160px);
            }
            
            #emailBodyEditor {
                min-height: 150px !important;
                max-height: 200px !important;
            }
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .professional-input {
            @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto p-8">
        <h1 class="text-3xl font-bold mb-8">Modal Test</h1>
        <button id="openModal" class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600">
            Open Email Preview Modal
        </button>
    </div>

    <!-- Email Preview Modal -->
    <div id="emailPreviewModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="modal-content w-full max-w-4xl max-h-[95vh] flex flex-col">
                <!-- Modal Header -->
                <div class="bg-gradient-to-r from-green-600 to-green-700 text-white p-6 flex-shrink-0">
                    <div class="flex justify-between items-center">
                        <h3 class="text-2xl font-bold">
                            <i class="fas fa-eye mr-3"></i>
                            Email Preview & Advanced Editor
                        </h3>
                        <button id="closePreviewModal" class="text-white hover:text-green-200 transition-colors">
                            <i class="fas fa-times text-2xl"></i>
                        </button>
                    </div>
                    <p class="text-green-100 mt-2 text-sm">
                        Review and customize your RFQ email before sending to stakeholders
                    </p>
                </div>

                <!-- Modal Body - Scrollable Content -->
                <div class="p-6 overflow-y-auto flex-1 min-h-0">
                    <!-- Email Details -->
                    <div class="space-y-6 mb-8">
                        <!-- Subject Line -->
                        <div class="form-group">
                            <label class="block text-sm font-semibold text-gray-700 mb-3">
                                <i class="fas fa-edit mr-2 text-blue-600"></i>Subject Line (Fully Editable)
                            </label>
                            <input type="text" id="previewSubject" class="professional-input w-full" 
                                   placeholder="Edit the email subject line..." value="Ligentec RFQ Request Automated" />
                        </div>

                        <!-- Recipients -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="form-group">
                                <label class="block text-sm font-semibold text-gray-700 mb-3">
                                    <i class="fas fa-users mr-2 text-blue-600"></i>To Recipients (Editable)
                                </label>
                                <input type="text" id="previewTo" class="professional-input w-full" 
                                       placeholder="Edit recipients (comma-separated)..." value="<EMAIL>" />
                            </div>
                            <div class="form-group">
                                <label class="block text-sm font-semibold text-gray-700 mb-3">
                                    <i class="fas fa-user-plus mr-2 text-blue-600"></i>CC Recipients (Editable)
                                </label>
                                <input type="text" id="previewCC" class="professional-input w-full" 
                                       placeholder="Edit CC recipients (comma-separated)..." 
                                       value="<EMAIL>, <EMAIL>, <EMAIL>" />
                            </div>
                        </div>
                    </div>

                    <!-- Email Body Editor -->
                    <div class="form-group mb-6">
                        <label class="block text-sm font-semibold text-gray-700 mb-3">
                            <i class="fas fa-edit mr-2 text-blue-600"></i>Email Body (Rich Text Editor)
                        </label>
                        <div class="border-2 border-gray-200 rounded-xl overflow-hidden">
                            <div id="emailBodyEditor" 
                                 class="min-h-[250px] max-h-[300px] p-6 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 overflow-y-auto"
                                 contenteditable="true" style="line-height: 1.8; font-size: 15px;">
                                <p>Dear Pascale,</p>
                                <br>
                                <p>Could you please send us a quotation for the following:</p>
                                <br>
                                <p>Here is the <a href="#" class="text-blue-600 underline">AN350_PA011_05-126_FAU_FULL_LOOP</a></p>
                                <br>
                                <p>Thank you for your time and assistance.</p>
                                <br>
                                <p>Best regards, Elisée Kajingu Test Technician LIGENTEC SAS France 224 Boulevard John Kennedy, 91100 Corbeil-Essonnes, France w: www.ligentec.com</p>
                            </div>
                        </div>
                        <div class="bg-amber-50 border border-amber-200 rounded-lg p-4 mt-4">
                            <div class="flex items-start">
                                <i class="fas fa-magic text-amber-600 mr-3 mt-1"></i>
                                <div>
                                    <h5 class="font-semibold text-amber-800 mb-1">Maximum Editing Flexibility</h5>
                                    <p class="text-sm text-amber-700">
                                        Edit any part of the email including subject, recipients, body content, links, and signature. All changes will be reflected in the final email sent to recipients.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Fixed Footer with Action Buttons -->
                <div class="border-t border-gray-200 p-6 bg-gray-50 flex-shrink-0">
                    <div class="flex flex-col sm:flex-row gap-4">
                        <button type="button" id="refreshPreviewBtn" 
                                class="flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-sync-alt mr-2"></i>Refresh Preview
                        </button>

                        <button type="button" id="sendFromPreviewBtn" 
                                class="flex-1 bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors">
                            <i class="fas fa-paper-plane mr-2"></i>Send Email
                        </button>

                        <button type="button" id="cancelPreviewBtn" 
                                class="flex-1 sm:flex-none bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors">
                            <i class="fas fa-times mr-2"></i>Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('openModal').addEventListener('click', function() {
            document.getElementById('emailPreviewModal').classList.remove('hidden');
        });

        document.getElementById('closePreviewModal').addEventListener('click', function() {
            document.getElementById('emailPreviewModal').classList.add('hidden');
        });

        document.getElementById('cancelPreviewBtn').addEventListener('click', function() {
            document.getElementById('emailPreviewModal').classList.add('hidden');
        });

        // Close modal when clicking outside
        document.getElementById('emailPreviewModal').addEventListener('click', function(e) {
            if (e.target === this) {
                this.classList.add('hidden');
            }
        });
    </script>
</body>
</html>
