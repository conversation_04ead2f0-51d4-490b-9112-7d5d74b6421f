"""
RFQ Email Service Module

This module provides email automation services for RFQ (Request for Quotation) processes.
Developed for Ligentec SA by <PERSON><PERSON> - RFQ Email Automation Feature
"""

import logging
import os
from datetime import datetime
from typing import Dict, Optional

from flask import current_app
from flask_mail import Message

# Configure logging
logger = logging.getLogger(__name__)

# Email configuration constants
RFQ_SENDER = ("<PERSON><PERSON>", "<EMAIL>")  # Display name and email address
RFQ_RECIPIENTS_PRODUCTION = ["<EMAIL>"]
RFQ_CC_PRODUCTION = [
    "Jean-<PERSON>.<PERSON>@xfab.com",
    "<EMAIL>",
    "<EMAIL>",
]
RFQ_RECIPIENTS_TEST = ["<EMAIL>"]
RFQ_CC_TEST = ["<EMAIL>"]


# Email template
EMAIL_SUBJECT = "RFQ Quotation Request LigentecFrance"
EMAIL_SIGNATURE = """Best regards,

<PERSON><PERSON><PERSON>

Test Technician

LIGENTEC SAS France

224 Boulevard <PERSON> Kennedy,

91100 Corbeil-<PERSON>, France

w: www.ligentec.com"""


class RFQEmailService:
    """Service class for handling RFQ email automation."""

    def __init__(self, mail_instance):
        """Initialize the RFQ Email Service.

        Args:
            mail_instance: Flask-Mail instance for sending emails
        """
        self.mail = mail_instance

    def send_form_based_rfq_email(
        self,
        form_data: Dict,
        pdf_file_path: Optional[str] = None,
        test_mode: bool = True,
    ) -> Dict:
        """Send RFQ email based on form data.

        Args:
            form_data: Dictionary containing form fields
            pdf_file_path: Optional path to PDF attachment
            test_mode: If True, send only to test recipients

        Returns:
            Summary of email sending results
        """
        results = {
            "total_records": 1,
            "emails_sent": 0,
            "errors": [],
            "errors_count": 0,
            "test_mode": test_mode,
        }

        try:
            # Generate automated subject line
            subject = self._generate_automated_subject(form_data)

            # Generate automated email body
            body = self._generate_automated_body(form_data)

            # Send the email
            self._send_form_based_email(subject, body, pdf_file_path, test_mode)

            results["emails_sent"] = 1
            results["errors_count"] = 0
            logger.info("Form-based RFQ email sent successfully")
        except Exception as e:
            error_msg = f"Failed to send form-based RFQ email: {str(e)}"
            results["errors"].append(error_msg)
            results["errors_count"] = 1
            logger.error(error_msg)

        return results

    def _generate_automated_subject(self, form_data: Dict) -> str:
        """Generate simplified automated subject line.

        Returns:
            Simple subject line: "Ligentec RFQ Request Automated"
        """
        logger.info("Generating simplified subject line")
        return "Ligentec RFQ Request Automated"

    def _generate_automated_body(self, form_data: Dict) -> str:
        """Generate automated email body with multiple clickable links and project titles.

        Args:
            form_data: Dictionary containing form fields including XFab URLs and project titles

        Returns:
            Formatted email body with HTML for clickable links and project descriptions
        """
        # Collect all XFab URLs and their corresponding project titles (up to 5)
        sow_items = []
        for i in range(1, 6):  # xfabCloudUrl1 through xfabCloudUrl5
            url_key = f"xfabCloudUrl{i}" if i > 1 else "xfabCloudUrl"
            title_key = f"sowTitle{i}"

            url = form_data.get(url_key, "").strip()
            title = form_data.get(title_key, "").strip()

            if url:
                sow_items.append(
                    {"url": url, "title": title if title else f"SoW {i}", "index": i}
                )
                logger.info(f"SoW {i} - Title: '{title}', URL: '{url}'")

        if not sow_items:
            logger.warning("No XFab URLs provided in form data")
            sow_items = [{"url": "#", "title": "SoW 1", "index": 1}]

        # Create links section with project titles
        if len(sow_items) == 1:
            item = sow_items[0]
            if item["title"] and item["title"] != f"SoW {item['index']}":
                links_section = (
                    f'Here is the <a href="{item["url"]}" target="_blank" '
                    f'style="color: #0066cc; text-decoration: underline;">{item["title"]}</a><br><br>'
                )
            else:
                links_section = (
                    f'Here is the <a href="{item["url"]}" target="_blank" '
                    f'style="color: #0066cc; text-decoration: underline;">SoW</a><br><br>'
                )
        else:
            links_html = []
            for item in sow_items:
                if item["title"] and item["title"] != f"SoW {item['index']}":
                    # Use project title as link text
                    link_html = (
                        f'<a href="{item["url"]}" target="_blank" '
                        f'style="color: #0066cc; text-decoration: underline;">{item["title"]}</a>'
                    )
                else:
                    # Use generic SoW numbering
                    link_html = (
                        f'<a href="{item["url"]}" target="_blank" '
                        f'style="color: #0066cc; text-decoration: underline;">SoW {item["index"]}</a>'
                    )
                links_html.append(link_html)
            links_section = f"Here are the SoWs:<br>{'<br>'.join(links_html)}<br><br>"

        # Create email body with clickable links
        body = f"""Dear Pascale,<br><br>

Could you please send us a quotation for the following:<br><br>

{links_section}

Thank you for your time and assistance.<br><br>

{EMAIL_SIGNATURE}"""

        logger.info(f"Generated email body with {len(sow_items)} SoW(s)")
        return body

    def _send_form_based_email(
        self, subject: str, body: str, pdf_file_path: Optional[str], test_mode: bool
    ):
        """Send form-based RFQ email.

        Args:
            subject: Email subject line
            body: Email body content (HTML)
            pdf_file_path: Optional path to PDF attachment
            test_mode: If True, send only to test recipients
        """
        # Determine recipients based on mode
        if test_mode:
            recipients = RFQ_RECIPIENTS_TEST
            cc_recipients = RFQ_CC_TEST
        else:
            recipients = RFQ_RECIPIENTS_PRODUCTION
            cc_recipients = RFQ_CC_PRODUCTION

        # Create and send message
        msg = Message(
            subject=subject,
            recipients=recipients,
            cc=cc_recipients,
            html=body,  # Use html parameter for clickable links
            sender=RFQ_SENDER,
        )

        # Attach PDF file if provided
        if pdf_file_path and os.path.exists(pdf_file_path):
            try:
                with open(pdf_file_path, "rb") as f:
                    msg.attach(
                        filename=os.path.basename(pdf_file_path),
                        content_type="application/pdf",
                        data=f.read(),
                    )
                logger.info(f"PDF attachment added: {os.path.basename(pdf_file_path)}")
            except Exception as e:
                logger.warning(f"Could not attach PDF file {pdf_file_path}: {str(e)}")

        self.mail.send(msg)

    def generate_email_preview(self, form_data: Dict) -> Dict:
        """Generate email preview data for editing.

        Args:
            form_data: Dictionary containing form fields

        Returns:
            Dictionary with email preview data
        """
        subject = self._generate_automated_subject(form_data)
        body = self._generate_automated_body(form_data)

        # Extract recipient information
        recipients = {"to": RFQ_RECIPIENTS_PRODUCTION, "cc": RFQ_CC_PRODUCTION}

        return {
            "subject": subject,
            "body": body,
            "recipients": recipients,
            "sender": RFQ_SENDER,
            "form_data": form_data,
        }

    def send_custom_email(
        self,
        subject: str,
        body: str,
        recipients: Dict,
        pdf_file_path: Optional[str] = None,
        test_mode: bool = True,
    ) -> Dict:
        """Send custom email with user-modified content.

        Args:
            subject: Custom email subject
            body: Custom email body (HTML)
            recipients: Dictionary with 'to' and 'cc' lists
            pdf_file_path: Optional path to PDF attachment
            test_mode: If True, send only to test recipients

        Returns:
            Summary of email sending results
        """
        results = {
            "total_records": 1,
            "emails_sent": 0,
            "errors": [],
            "errors_count": 0,
            "test_mode": test_mode,
        }

        try:
            # Override recipients if in test mode
            if test_mode:
                to_recipients = RFQ_RECIPIENTS_TEST
                cc_recipients = RFQ_CC_TEST
            else:
                to_recipients = recipients.get("to", RFQ_RECIPIENTS_PRODUCTION)
                cc_recipients = recipients.get("cc", RFQ_CC_PRODUCTION)

            # Create and send message
            msg = Message(
                subject=subject,
                recipients=to_recipients,
                cc=cc_recipients,
                html=body,
                sender=RFQ_SENDER,
            )

            # Attach PDF file if provided
            if pdf_file_path and os.path.exists(pdf_file_path):
                try:
                    with open(pdf_file_path, "rb") as f:
                        msg.attach(
                            filename=os.path.basename(pdf_file_path),
                            content_type="application/pdf",
                            data=f.read(),
                        )
                    logger.info(
                        f"PDF attachment added: {os.path.basename(pdf_file_path)}"
                    )
                except Exception as e:
                    logger.warning(
                        f"Could not attach PDF file {pdf_file_path}: {str(e)}"
                    )

            self.mail.send(msg)
            results["emails_sent"] = 1
            results["errors_count"] = 0
            logger.info("Custom RFQ email sent successfully")
        except Exception as e:
            error_msg = f"Failed to send custom RFQ email: {str(e)}"
            results["errors"].append(error_msg)
            results["errors_count"] = 1
            logger.error(error_msg)

        return results

    def cleanup_old_files(self, days_old: int = 30):
        """Clean up old uploaded files.

        Args:
            days_old: Remove files older than this many days
        """
        # This method is kept for compatibility but no longer needed
        # since we're not handling file uploads anymore
        logger.info("File cleanup skipped - no file uploads in current implementation")
