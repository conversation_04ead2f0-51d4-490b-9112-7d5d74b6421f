# send_email.py
from datetime import datetime, timedelta
from typing import Dict, List, Optional

from flask import current_app, session
from flask_mail import Mail, Message

# Initialize Mail object
mail = Mail()

# Default email settings
# Primary recipient
DEFAULT_RECIPIENTS = ["<EMAIL>", "<EMAIL>"]
# Add recipients to the CC list as needed.
DEFAULT_CC = ["****************", "<EMAIL>", "<EMAIL>"]

# Test mode recipients (for preview/testing) - only your email for safe testing
TEST_RECIPIENTS = ["<EMAIL>"]
TEST_CC = []


def get_tracking_link(tracking_number: str) -> str:
    """
    Generate tracking link based on the tracking number format.
    """
    tracking_number = tracking_number.strip()
    if tracking_number.startswith("1Z"):  # UPS
        return f"https://www.ups.com/track?tracknum={tracking_number}"
    elif tracking_number.startswith(("7", "8")):  # FedEx
        return f"https://www.fedex.com/fedextrack/?trknbr={tracking_number}"
    elif len(tracking_number) == 10 or tracking_number.startswith(("JD", "DHL")):  # DHL
        return f"https://www.dhl.com/track?tracking-number={tracking_number}"
    else:
        # Default to UPS as LIGENTEC currently uses UPS for shipping
        return f"https://www.ups.com/track?tracknum={tracking_number}"


def send_shipping_notification(
    tracking_number: str,
    ligentec_label_title: str,
    wafer_ids: List[str],
    manager_acronym: Optional[str] = None,
    contact_email: Optional[str] = None,
    additional_cc: Optional[List[str]] = None,
) -> None:
    """
    Sends shipment notification email.

    Args:
        tracking_number: Carrier tracking number
        ligentec_label_title: Title from Asana task
        wafer_ids: List of wafer IDs being shipped
        manager_acronym: Account/Project manager acronym
        contact_email: Contact person email
        additional_cc: Additional CC recipients
    """
    try:
        # Build CC list
        cc_list = list(DEFAULT_CC)

        # Add manager email if provided
        if manager_acronym:
            manager_email = f"{manager_acronym.lower()}@ligentec.com"
            if manager_email not in cc_list:
                cc_list.append(manager_email)

        # Add contact email if provided
        if contact_email and contact_email not in cc_list:
            cc_list.append(contact_email)

        # Add additional CC recipients
        if additional_cc:
            for email in additional_cc:
                if email not in cc_list:
                    cc_list.append(email)

        # Clean tracking number and get link
        clean_tracking = tracking_number.replace("Track", "").strip()
        tracking_link = get_tracking_link(clean_tracking)

        # Calculate delivery date
        delivery_date = datetime.now() + timedelta(days=3)
        formatted_date = f"{delivery_date.strftime('%B %d, %Y')}"

        # Format wafer list
        wafer_list = "\n".join(f"- {wafer_id}" for wafer_id in wafer_ids)

        # Create email content
        body = f"""
The above-mentioned order has been shipped.

Tracking Number: {clean_tracking}
Track your shipment: {tracking_link}

Please find enclosed the wafer IDs list:
{wafer_list}

With estimated delivery date {formatted_date}

Best regards,
Elisee Kajingu
Test Technician
Ligentec France
"""

        msg = Message(
            subject=f"Shipping Notification: {ligentec_label_title}",
            recipients=DEFAULT_RECIPIENTS,
            cc=cc_list,
            body=body,
            sender=current_app.config["MAIL_DEFAULT_SENDER"],
        )

        mail.send(msg)
        current_app.logger.info(
            (
                f"Shipping notification sent successfully with tracking number: "
                f"{clean_tracking}"
            )
        )

        # Create in-app notification
        try:
            from core.models.notification_models import EmailNotificationData
            from core.services.notification_service import notification_service

            user_id = session.get("user_id")
            if user_id:
                email_data = EmailNotificationData(
                    subject=f"Shipping Notification: {ligentec_label_title}",
                    sender=current_app.config["MAIL_DEFAULT_SENDER"],
                    recipients=DEFAULT_RECIPIENTS,
                    cc=cc_list,
                    body=body[:200] + "..." if len(body) > 200 else body,
                    related_entity_type="shipment",
                    related_entity_id=clean_tracking,
                )
                notification_service.create_email_notification(user_id, email_data)
        except Exception as e:
            current_app.logger.warning(
                f"Failed to create in-app notification: {str(e)}"
            )

    except Exception as e:
        current_app.logger.error(f"Failed to send shipping notification: {str(e)}")
        raise


def generate_shipping_email_preview(email_data: Dict) -> Dict:
    """
    Generate email preview data for Eiger shipping notifications.

    Args:
        email_data: Dictionary containing email parameters

    Returns:
        Dictionary with email preview data
    """
    try:
        # Extract data
        tracking_number = email_data.get("tracking_number", "")
        ligentec_label_title = email_data.get("ligentec_label_title", "")
        wafer_ids = email_data.get("wafer_ids", [])
        manager_acronym = email_data.get("manager_acronym")
        contact_email = email_data.get("contact_email")
        additional_cc = email_data.get("additional_cc", [])

        # Build CC list
        cc_list = list(DEFAULT_CC)

        # Add manager email if provided
        if manager_acronym:
            manager_email = f"{manager_acronym.lower()}@ligentec.com"
            if manager_email not in cc_list:
                cc_list.append(manager_email)

        # Add contact email if provided
        if contact_email and contact_email not in cc_list:
            cc_list.append(contact_email)

        # Add additional CC recipients
        if additional_cc:
            for email in additional_cc:
                if email not in cc_list:
                    cc_list.append(email)

        # Clean tracking number and get link
        clean_tracking = tracking_number.replace("Track", "").strip()
        tracking_link = get_tracking_link(clean_tracking)

        # Calculate delivery date
        delivery_date = datetime.now() + timedelta(days=3)
        formatted_date = f"{delivery_date.strftime('%B %d, %Y')}"

        # Format wafer list
        wafer_list = "\n".join(f"- {wafer_id}" for wafer_id in wafer_ids)

        # Create email content
        body = f"""The above-mentioned order has been shipped.

Tracking Number: {clean_tracking}
Track your shipment: {tracking_link}

Please find enclosed the wafer IDs list:
{wafer_list}

With estimated delivery date {formatted_date}

Best regards,
Elisee Kajingu
Test Technician
Ligentec France"""

        # Generate subject
        subject = f"Shipping Notification: {ligentec_label_title}"

        return {
            "success": True,
            "preview": {
                "subject": subject,
                "body": body,
                "recipients": {"to": DEFAULT_RECIPIENTS, "cc": cc_list},
                "sender": current_app.config["MAIL_DEFAULT_SENDER"],
                "tracking_number": clean_tracking,
                "tracking_link": tracking_link,
                "delivery_date": formatted_date,
                "wafer_count": len(wafer_ids),
            },
        }

    except Exception as e:
        current_app.logger.error(f"Failed to generate email preview: {str(e)}")
        return {"success": False, "message": f"Failed to generate preview: {str(e)}"}


def send_custom_shipping_notification(
    email_data: Dict, test_mode: bool = False
) -> Dict:
    """
    Send custom shipping notification with user-edited content.

    Args:
        email_data: Dictionary containing custom email data
        test_mode: If True, send only to test recipients

    Returns:
        Dictionary with success status and message
    """
    try:
        # Extract custom email data
        subject = email_data.get("subject", "")
        body = email_data.get("body", "")
        recipients_data = email_data.get("recipients", {})

        # Determine recipients based on mode
        if test_mode:
            recipients = TEST_RECIPIENTS
            cc_list = TEST_CC
        else:
            # Parse recipients from the preview data
            to_emails = recipients_data.get("to", DEFAULT_RECIPIENTS)
            cc_emails = recipients_data.get("cc", DEFAULT_CC)

            # Ensure they're lists
            recipients = to_emails if isinstance(to_emails, list) else [to_emails]
            cc_list = cc_emails if isinstance(cc_emails, list) else [cc_emails]

        # Create and send message
        msg = Message(
            subject=subject,
            recipients=recipients,
            cc=cc_list,
            body=body,
            sender=current_app.config["MAIL_DEFAULT_SENDER"],
        )

        mail.send(msg)

        # Log success
        mode_text = "test" if test_mode else "production"
        current_app.logger.info(
            f"Custom shipping notification sent successfully in {mode_text} mode"
        )

        # Create in-app notification
        try:
            from core.models.notification_models import EmailNotificationData
            from core.services.notification_service import notification_service

            user_id = session.get("user_id")
            if user_id:
                email_notification_data = EmailNotificationData(
                    subject=subject,
                    sender=current_app.config["MAIL_DEFAULT_SENDER"],
                    recipients=recipients,
                    cc=cc_list,
                    body=body[:200] + "..." if len(body) > 200 else body,
                    related_entity_type="shipment",
                    related_entity_id=email_data.get("tracking_number", "custom"),
                )
                notification_service.create_email_notification(
                    user_id, email_notification_data
                )
        except Exception as e:
            current_app.logger.warning(
                f"Failed to create in-app notification: {str(e)}"
            )

        return {
            "success": True,
            "message": f"Custom shipping notification sent successfully in {mode_text} mode",
        }

    except Exception as e:
        current_app.logger.error(
            f"Failed to send custom shipping notification: {str(e)}"
        )
        return {"success": False, "message": f"Failed to send email: {str(e)}"}


def send_test_email() -> None:
    """Sends a test email to verify configuration - only to specific recipients"""
    try:
        sender_email = current_app.config["MAIL_DEFAULT_SENDER"]

        # Filter the CC list to keep only elk and khk
        filtered_cc = ["<EMAIL>", "<EMAIL>"]

        msg = Message(
            subject="Test Email - Ligentec Labeller",
            recipients=[sender_email],  # Only send to the sender's email
            cc=filtered_cc,  # Only include elk and khk
            body=(
                "This is a test email to verify the email configuration is "
                "working correctly.\n\n"
                "This is a test email sent to you and selected Ligentec contacts."
            ),
            sender=sender_email,
        )

        mail.send(msg)
        current_app.logger.info(f"Test email sent successfully to {sender_email}")

        # Create in-app notification
        try:
            from core.models.notification_models import EmailNotificationData
            from core.services.notification_service import notification_service

            user_id = session.get("user_id")
            if user_id:
                email_data = EmailNotificationData(
                    subject="Test Email - Ligentec Labeller",
                    sender=sender_email,
                    recipients=[sender_email],
                    cc=filtered_cc,
                    body="Test email sent successfully to verify email configuration.",
                    related_entity_type="system",
                    related_entity_id="test_email",
                )
                notification_service.create_email_notification(user_id, email_data)
        except Exception as e:
            current_app.logger.warning(
                f"Failed to create in-app notification: {str(e)}"
            )

    except Exception as e:
        current_app.logger.error(f"Failed to send test email: {str(e)}")
        raise
