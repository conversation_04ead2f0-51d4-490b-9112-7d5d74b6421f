import enum
from datetime import datetime
from typing import Dict, Optional

from flask_login import UserMixin  # noqa: F401
from pydantic import BaseModel, Field
from sqlalchemy import (
    TIMESTAMP,
    Column,
    Enum,
    ForeignKey,
    Numeric,
    SmallInteger,
    String,
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import declarative_base, relationship

# SQLAlchemy Models
Base = declarative_base()


class ShipmentStatus(enum.Enum):
    DRAFT = "draft"
    PENDING = "pending"
    IN_PREPARATION = "in_preparation"
    PACKAGED = "packaged"
    WAITING_REVIEW = "waiting_review"
    SHIPPED = "shipped"
    DELIVERED = "delivered"
    CANCELLED = "cancelled"


class Wafer(Base):
    __tablename__ = "wafers"

    wafer_id = Column(String(11), primary_key=True)
    lot_id = Column(String)
    size = Column(Numeric)
    wafer_metadata = Column("metadata", JSONB)
    updated_at = Column(TIMESTAMP(timezone=True))
    updated_by = Column(String)

    inventory = relationship("WaferInventory", back_populates="wafer")


class Location(Base):
    __tablename__ = "locations"

    location_id = Column(String, primary_key=True)
    label = Column(String)
    address = Column(String)
    telephone = Column(String)
    email = Column(String)
    contact_person = Column(String)
    asana_link = Column(String)
    updated_at = Column(TIMESTAMP(timezone=True))
    updated_by = Column(String)

    wafer_inventories = relationship("WaferInventory", back_populates="location")


class WaferInventory(Base):
    __tablename__ = "wafer_inventory"

    wafer_id = Column(String, ForeignKey("wafers.wafer_id"), primary_key=True)
    cassette_id = Column(String)
    slot_id = Column(SmallInteger)
    arrived_at = Column(TIMESTAMP(timezone=True))
    sent_at = Column(TIMESTAMP(timezone=True))
    location_id = Column(String, ForeignKey("locations.location_id"))
    inventory_metadata = Column("metadata", JSONB)
    updated_at = Column(TIMESTAMP(timezone=True))
    updated_by = Column(String)

    wafer = relationship("Wafer", back_populates="inventory")
    location = relationship("Location", back_populates="wafer_inventories")


# Pydantic Models for API Validation
class WaferInventoryModel(BaseModel):
    """Pydantic model for Wafer Inventory table validation and serialization."""

    wafer_id: str = Field(max_length=15)
    cassette_id: str = Field(max_length=30)
    slot_id: int
    arrived_at: Optional[datetime] = None
    sent_at: Optional[datetime] = None
    location_id: Optional[str] = Field(max_length=30)
    metadata: Optional[Dict] = None
    updated_at: Optional[datetime] = None
    updated_by: Optional[str] = Field(max_length=200)

    class Config:
        """Configuration for Pydantic model."""

        from_attributes = True  # Allows conversion from SQLAlchemy models


# Additional Pydantic models for other tables if needed
class LocationModel(BaseModel):
    """Pydantic model for Location table validation and serialization."""

    location_id: str
    label: str = Field(max_length=50)
    address: str = Field(max_length=200)
    email: str = Field(max_length=100)
    telephone: str = Field(max_length=20)
    contact_person: str = Field(max_length=50)
    asana_link: Optional[str] = None
    updated_at: Optional[datetime] = None
    updated_by: Optional[str] = Field(max_length=200)

    class Config:
        from_attributes = True


class WaferModel(BaseModel):
    """Pydantic model for Wafer table validation and serialization."""

    wafer_id: str = Field(max_length=11)
    lot_id: Optional[str] = None
    size: Optional[float] = None
    metadata: Optional[Dict] = None
    updated_at: Optional[datetime] = None
    updated_by: Optional[str] = Field(max_length=200)

    class Config:
        from_attributes = True


class ShipmentWafer(Base):
    __tablename__ = "shipment_wafers"

    id = Column(String, primary_key=True)
    shipment_id = Column(String, ForeignKey("shipments.shipment_id"))
    wafer_id = Column(String, ForeignKey("wafer_inventory.wafer_id"))
    slot_id = Column(String)
    original_location_id = Column(String, ForeignKey("locations.location_id"))
    shipment_wafer_metadata = Column("metadata", JSONB)
    created_at = Column(TIMESTAMP(timezone=True))
    updated_at = Column(TIMESTAMP(timezone=True))

    # Relationships
    shipment = relationship("Shipment", back_populates="wafers")
    wafer = relationship("WaferInventory")
    original_location = relationship("Location")


# Add Pydantic models for the new shipment tables


class ShipmentModel(BaseModel):
    """Pydantic model for Shipment table validation and serialization."""

    shipment_id: str
    tracking_number: Optional[str] = None
    status: ShipmentStatus
    destination_location_id: str
    origin_location_id: str
    asana_task_id: Optional[str] = None
    label_path: Optional[str] = None
    packing_slip_path: Optional[str] = None
    metadata: Optional[Dict] = None
    ship_date: Optional[datetime] = None
    delivery_date: Optional[datetime] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    created_by: Optional[str] = Field(max_length=200)
    updated_by: Optional[str] = Field(max_length=200)

    class Config:
        from_attributes = True


class ShipmentWaferModel(BaseModel):
    """Pydantic model for ShipmentWafer table validation and serialization."""

    id: str
    shipment_id: str
    wafer_id: str
    slot_id: Optional[str] = None
    original_location_id: str
    metadata: Optional[Dict] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# Add this to your models.py, alongside your other models


class Shipment(Base):
    __tablename__ = "shipments"

    shipment_id = Column(String, primary_key=True)
    tracking_number = Column(String)
    status = Column(Enum(ShipmentStatus))
    destination_location_id = Column(String, ForeignKey("locations.location_id"))
    origin_location_id = Column(String, ForeignKey("locations.location_id"))
    asana_task_id = Column(String)
    label_path = Column(String)
    packing_slip_path = Column(String)
    shipment_metadata = Column("metadata", JSONB)
    ship_date = Column(TIMESTAMP(timezone=True))
    delivery_date = Column(TIMESTAMP(timezone=True))
    created_at = Column(TIMESTAMP(timezone=True))
    updated_at = Column(TIMESTAMP(timezone=True))
    created_by = Column(String)
    updated_by = Column(String)

    # Relationships
    destination = relationship("Location", foreign_keys=[destination_location_id])
    origin = relationship("Location", foreign_keys=[origin_location_id])
    wafers = relationship("ShipmentWafer", back_populates="shipment")
