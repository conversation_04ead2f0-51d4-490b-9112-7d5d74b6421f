#!/bin/bash
#
# Setup Script for Talaria Dashboard Server Monitoring
# This script installs and configures automated disk space monitoring and cleanup
# on your deployment server.
#
# Usage: ./setup-server-monitoring.sh [--email <EMAIL>] [--deployment-base /opt/deployments]
#

set -euo pipefail

# Configuration
EMAIL_ALERT=""
DEPLOYMENT_BASE="/opt/deployments"
SCRIPTS_DIR="/usr/local/bin"
LOG_DIR="/var/log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --email)
            EMAIL_ALERT="$2"
            shift 2
            ;;
        --deployment-base)
            DEPLOYMENT_BASE="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [--email <EMAIL>] [--deployment-base /opt/deployments]"
            echo "  --email           : Email address for disk space alerts"
            echo "  --deployment-base : Base directory for deployments (default: /opt/deployments)"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Logging function
log() {
    echo -e "$(date '+%Y-%m-%d %H:%M:%S'): $1"
}

# Check if running as root
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log "${RED}ERROR: This script must be run as root${NC}"
        log "Please run: sudo $0"
        exit 1
    fi
}

# Install required packages
install_packages() {
    log "${BLUE}📦 Installing required packages...${NC}"
    
    if command -v apt-get >/dev/null 2>&1; then
        # Ubuntu/Debian
        apt-get update
        apt-get install -y curl wget cron logrotate
        
        # Install mail utility if email alerts are configured
        if [ -n "$EMAIL_ALERT" ]; then
            apt-get install -y mailutils
        fi
        
    elif command -v yum >/dev/null 2>&1; then
        # RHEL/CentOS
        yum update -y
        yum install -y curl wget cronie logrotate
        
        # Install mail utility if email alerts are configured
        if [ -n "$EMAIL_ALERT" ]; then
            yum install -y mailx
        fi
        
    elif command -v dnf >/dev/null 2>&1; then
        # Fedora
        dnf update -y
        dnf install -y curl wget cronie logrotate
        
        # Install mail utility if email alerts are configured
        if [ -n "$EMAIL_ALERT" ]; then
            dnf install -y mailx
        fi
    else
        log "${YELLOW}⚠️  Could not detect package manager. Please install curl, wget, cron, and logrotate manually.${NC}"
    fi
}

# Copy scripts to system location
install_scripts() {
    log "${BLUE}📋 Installing monitoring scripts...${NC}"
    
    # Copy cleanup script
    cp "$(dirname "$0")/docker-cleanup.sh" "$SCRIPTS_DIR/"
    chmod +x "$SCRIPTS_DIR/docker-cleanup.sh"
    log "✅ Installed: $SCRIPTS_DIR/docker-cleanup.sh"
    
    # Copy monitoring script
    cp "$(dirname "$0")/disk-monitor.sh" "$SCRIPTS_DIR/"
    chmod +x "$SCRIPTS_DIR/disk-monitor.sh"
    log "✅ Installed: $SCRIPTS_DIR/disk-monitor.sh"
    
    # Create configuration file
    cat > /etc/default/talaria-monitoring << EOF
# Talaria Dashboard Monitoring Configuration
DEPLOYMENT_BASE="$DEPLOYMENT_BASE"
DISK_THRESHOLD=85
DISK_CRITICAL=95
RETENTION_HOURS=48
EMAIL_ALERT="$EMAIL_ALERT"
EOF
    log "✅ Created: /etc/default/talaria-monitoring"
}

# Setup cron jobs
setup_cron() {
    log "${BLUE}⏰ Setting up cron jobs...${NC}"
    
    # Create cron job for daily cleanup
    cat > /etc/cron.d/talaria-cleanup << EOF
# Talaria Dashboard - Daily Docker cleanup at 2 AM
0 2 * * * root /usr/local/bin/docker-cleanup.sh --force >> /var/log/docker-cleanup.log 2>&1

# Talaria Dashboard - Hourly disk monitoring
0 * * * * root /usr/local/bin/disk-monitor.sh >> /var/log/disk-monitor.log 2>&1
EOF
    
    log "✅ Created: /etc/cron.d/talaria-cleanup"
    log "   - Daily cleanup: 2:00 AM"
    log "   - Hourly monitoring: Every hour"
    
    # Restart cron service
    if systemctl is-active --quiet cron; then
        systemctl restart cron
        log "✅ Restarted cron service"
    elif systemctl is-active --quiet crond; then
        systemctl restart crond
        log "✅ Restarted crond service"
    fi
}

# Setup log rotation
setup_logrotate() {
    log "${BLUE}📄 Setting up log rotation...${NC}"
    
    cat > /etc/logrotate.d/talaria-monitoring << EOF
/var/log/docker-cleanup.log /var/log/disk-monitor.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
}
EOF
    
    log "✅ Created: /etc/logrotate.d/talaria-monitoring"
}

# Configure Docker daemon for better disk management
configure_docker() {
    log "${BLUE}🐳 Configuring Docker daemon for better disk management...${NC}"
    
    # Backup existing config
    if [ -f /etc/docker/daemon.json ]; then
        cp /etc/docker/daemon.json /etc/docker/daemon.json.backup
        log "✅ Backed up existing Docker config"
    fi
    
    # Create or update Docker daemon config
    cat > /etc/docker/daemon.json << EOF
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  },
  "storage-driver": "overlay2",
  "live-restore": true,
  "default-ulimits": {
    "nofile": {
      "Hard": 64000,
      "Name": "nofile",
      "Soft": 64000
    }
  }
}
EOF
    
    log "✅ Updated Docker daemon configuration"
    
    # Restart Docker service
    if systemctl is-active --quiet docker; then
        log "${YELLOW}⚠️  Docker service restart required for changes to take effect${NC}"
        log "   Run: sudo systemctl restart docker"
    fi
}

# Create deployment directory structure
create_directories() {
    log "${BLUE}📁 Creating directory structure...${NC}"
    
    # Create deployment base directory
    mkdir -p "$DEPLOYMENT_BASE"
    log "✅ Created: $DEPLOYMENT_BASE"
    
    # Create log directories
    mkdir -p "$LOG_DIR"
    touch "$LOG_DIR/docker-cleanup.log"
    touch "$LOG_DIR/disk-monitor.log"
    log "✅ Created log files"
}

# Test the installation
test_installation() {
    log "${BLUE}🧪 Testing installation...${NC}"
    
    # Test cleanup script
    if "$SCRIPTS_DIR/docker-cleanup.sh" --help >/dev/null 2>&1; then
        log "✅ Cleanup script test: PASSED"
    else
        log "${RED}❌ Cleanup script test: FAILED${NC}"
        return 1
    fi
    
    # Test monitoring script
    if "$SCRIPTS_DIR/disk-monitor.sh" --help >/dev/null 2>&1; then
        log "✅ Monitoring script test: PASSED"
    else
        log "${RED}❌ Monitoring script test: FAILED${NC}"
        return 1
    fi
    
    # Test cron syntax
    if crontab -l -u root | grep -q talaria; then
        log "✅ Cron jobs test: PASSED"
    else
        log "${YELLOW}⚠️  Cron jobs test: Not found (may be in /etc/cron.d/)${NC}"
    fi
    
    # Run a test monitoring check
    log "${BLUE}🔍 Running test monitoring check...${NC}"
    "$SCRIPTS_DIR/disk-monitor.sh"
    
    return 0
}

# Display setup summary
show_summary() {
    log "${GREEN}🎉 Talaria Dashboard Server Monitoring Setup Complete!${NC}"
    echo
    log "${BLUE}📋 Setup Summary:${NC}"
    log "   ✅ Installed cleanup script: $SCRIPTS_DIR/docker-cleanup.sh"
    log "   ✅ Installed monitoring script: $SCRIPTS_DIR/disk-monitor.sh"
    log "   ✅ Configured cron jobs (daily cleanup, hourly monitoring)"
    log "   ✅ Setup log rotation"
    log "   ✅ Configured Docker daemon"
    log "   ✅ Created deployment directories"
    echo
    log "${BLUE}📊 Monitoring Configuration:${NC}"
    log "   • Warning threshold: 85%"
    log "   • Critical threshold: 95%"
    log "   • Cleanup retention: 48 hours"
    log "   • Deployment base: $DEPLOYMENT_BASE"
    if [ -n "$EMAIL_ALERT" ]; then
        log "   • Email alerts: $EMAIL_ALERT"
    else
        log "   • Email alerts: Not configured"
    fi
    echo
    log "${BLUE}📁 Log Files:${NC}"
    log "   • Cleanup logs: /var/log/docker-cleanup.log"
    log "   • Monitoring logs: /var/log/disk-monitor.log"
    echo
    log "${BLUE}🔧 Manual Commands:${NC}"
    log "   • Run cleanup: sudo $SCRIPTS_DIR/docker-cleanup.sh"
    log "   • Check disk: sudo $SCRIPTS_DIR/disk-monitor.sh"
    log "   • View cleanup logs: sudo tail -f /var/log/docker-cleanup.log"
    log "   • View monitoring logs: sudo tail -f /var/log/disk-monitor.log"
    echo
    log "${YELLOW}⚠️  IMPORTANT: Restart Docker daemon to apply configuration changes:${NC}"
    log "   sudo systemctl restart docker"
}

# Main execution
main() {
    log "${BLUE}🚀 Starting Talaria Dashboard Server Monitoring Setup${NC}"
    
    # Preliminary checks
    check_root
    
    # Installation steps
    install_packages
    create_directories
    install_scripts
    setup_cron
    setup_logrotate
    configure_docker
    
    # Test installation
    if test_installation; then
        show_summary
        log "${GREEN}✅ Setup completed successfully!${NC}"
        exit 0
    else
        log "${RED}❌ Setup completed with errors. Please check the logs.${NC}"
        exit 1
    fi
}

# Execute main function
main "$@"