#!/bin/bash
#
# Docker Cleanup Script for Talaria Dashboard Deployment Server
# This script automatically cleans up Docker images, containers, and other resources
# to prevent disk space issues during deployments.
#
# Usage: ./docker-cleanup.sh [--force] [--verbose]
#

set -euo pipefail

# Configuration
RETENTION_HOURS=${RETENTION_HOURS:-48}
LOG_FILE="/var/log/docker-cleanup.log"
FORCE_MODE=false
VERBOSE_MODE=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --force)
            FORCE_MODE=true
            shift
            ;;
        --verbose)
            VERBOSE_MODE=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [--force] [--verbose]"
            echo "  --force   : Skip confirmation prompts"
            echo "  --verbose : Show detailed output"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Logging function
log() {
    local message="$(date '+%Y-%m-%d %H:%M:%S'): $1"
    echo -e "$message" | tee -a "$LOG_FILE"
}

# Verbose logging function
vlog() {
    if [ "$VERBOSE_MODE" = true ]; then
        log "$1"
    fi
}

# Error handling
error_exit() {
    log "${RED}ERROR: $1${NC}"
    exit 1
}

# Check if running as root or with docker permissions
check_permissions() {
    if ! docker info >/dev/null 2>&1; then
        error_exit "Cannot connect to Docker daemon. Please run as root or ensure user is in docker group."
    fi
}

# Get disk usage
get_disk_usage() {
    df /var/lib/docker 2>/dev/null | tail -1 | awk '{print $5}' | sed 's/%//' || \
    df / | tail -1 | awk '{print $5}' | sed 's/%//'
}

# Get available space in MB
get_available_space() {
    df /var/lib/docker --output=avail 2>/dev/null | tail -1 | awk '{print int($1/1024)}' || \
    df / --output=avail | tail -1 | awk '{print int($1/1024)}'
}

# Main cleanup function
cleanup_docker() {
    log "${BLUE}🧹 Starting Docker cleanup process...${NC}"
    
    # Show initial disk usage
    local initial_usage=$(get_disk_usage)
    local initial_space=$(get_available_space)
    log "${YELLOW}📊 Initial disk usage: ${initial_usage}% (${initial_space}MB available)${NC}"
    
    # Stop and remove exited containers
    vlog "🗑️  Removing stopped containers..."
    docker container prune -f --filter "until=${RETENTION_HOURS}h" || true
    
    # Remove unused images
    vlog "🖼️  Removing unused images older than ${RETENTION_HOURS} hours..."
    docker image prune -af --filter "until=${RETENTION_HOURS}h" || true
    
    # Remove unused volumes
    vlog "💾 Removing unused volumes..."
    docker volume prune -f || true
    
    # Remove unused networks
    vlog "🌐 Removing unused networks..."
    docker network prune -f || true
    
    # Clean build cache
    vlog "🏗️  Cleaning build cache..."
    docker builder prune -af --filter "until=72h" || true
    
    # Remove dangling images
    vlog "🎭 Removing dangling images..."
    docker images -f "dangling=true" -q | xargs -r docker rmi || true
    
    # Show final disk usage
    local final_usage=$(get_disk_usage)
    local final_space=$(get_available_space)
    local space_freed=$((final_space - initial_space))
    
    log "${GREEN}✅ Cleanup completed!${NC}"
    log "${GREEN}📊 Final disk usage: ${final_usage}% (${final_space}MB available)${NC}"
    
    if [ $space_freed -gt 0 ]; then
        log "${GREEN}💾 Space freed: ${space_freed}MB${NC}"
    else
        log "${YELLOW}💾 No additional space freed${NC}"
    fi
    
    # Check if we still have low disk space
    if [ "$final_usage" -gt 90 ]; then
        log "${RED}⚠️  WARNING: Disk usage still high (${final_usage}%). Manual intervention may be needed.${NC}"
        return 1
    elif [ "$final_usage" -gt 80 ]; then
        log "${YELLOW}⚠️  CAUTION: Disk usage moderate (${final_usage}%). Monitor closely.${NC}"
    fi
    
    return 0
}

# Cleanup old deployment directories
cleanup_deployments() {
    local deployment_base="${DEPLOYMENT_BASE:-/opt/deployments}"
    
    if [ -d "$deployment_base" ]; then
        log "${BLUE}📁 Cleaning up old deployment directories...${NC}"
        vlog "Deployment base: $deployment_base"
        
        # Keep only the 3 most recent deployments
        cd "$deployment_base" 2>/dev/null && \
        ls -t | tail -n +4 | xargs rm -rf 2>/dev/null || true
        
        local remaining=$(ls -1 "$deployment_base" 2>/dev/null | wc -l)
        log "${GREEN}📁 Deployments remaining: $remaining${NC}"
    else
        vlog "📁 Deployment directory not found: $deployment_base"
    fi
}

# Cleanup system logs
cleanup_logs() {
    log "${BLUE}📝 Cleaning up system logs...${NC}"
    
    # Clean journal logs older than 7 days
    if command -v journalctl >/dev/null 2>&1; then
        vlog "🗞️  Cleaning systemd journal logs..."
        journalctl --vacuum-time=7d >/dev/null 2>&1 || true
    fi
    
    # Clean old log files
    vlog "📄 Cleaning old log files..."
    find /var/log -name "*.log" -mtime +7 -delete 2>/dev/null || true
    find /var/log -name "*.log.*" -mtime +3 -delete 2>/dev/null || true
}

# Main execution
main() {
    log "${BLUE}🚀 Starting Talaria Dashboard cleanup script${NC}"
    
    # Check permissions
    check_permissions
    
    # Create log directory if it doesn't exist
    mkdir -p "$(dirname "$LOG_FILE")"
    
    # Ask for confirmation unless in force mode
    if [ "$FORCE_MODE" = false ]; then
        echo -e "${YELLOW}This will clean up Docker containers, images, and volumes older than ${RETENTION_HOURS} hours.${NC}"
        read -p "Continue? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log "${YELLOW}Cleanup cancelled by user.${NC}"
            exit 0
        fi
    fi
    
    # Perform cleanup operations
    cleanup_docker
    cleanup_deployments
    cleanup_logs
    
    log "${GREEN}🎉 All cleanup operations completed successfully!${NC}"
}

# Execute main function
main "$@"