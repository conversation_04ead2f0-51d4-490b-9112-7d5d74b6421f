#!/bin/bash
#
# Disk Space Monitor for Talaria Dashboard Deployment Server
# This script monitors disk usage and automatically triggers cleanup when needed.
# It also sends alerts when disk space is running low.
#
# Usage: ./disk-monitor.sh [--threshold 85] [--critical 95] [--email <EMAIL>]
#

set -euo pipefail

# Default configuration
THRESHOLD=${DISK_THRESHOLD:-85}      # Warning threshold (%)
CRITICAL=${DISK_CRITICAL:-95}        # Critical threshold (%)
EMAIL_ALERT=""                       # Email for alerts
LOG_FILE="/var/log/disk-monitor.log"
CLEANUP_SCRIPT="/usr/local/bin/docker-cleanup.sh"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --threshold)
            THRESHOLD="$2"
            shift 2
            ;;
        --critical)
            CRITICAL="$2"
            shift 2
            ;;
        --email)
            EMAIL_ALERT="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [--threshold 85] [--critical 95] [--email <EMAIL>]"
            echo "  --threshold : Warning threshold percentage (default: 85)"
            echo "  --critical  : Critical threshold percentage (default: 95)"
            echo "  --email     : Email address for alerts"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Logging function
log() {
    local message="$(date '+%Y-%m-%d %H:%M:%S'): $1"
    echo -e "$message" | tee -a "$LOG_FILE"
}

# Get disk usage percentage
get_disk_usage() {
    df /var/lib/docker 2>/dev/null | tail -1 | awk '{print $5}' | sed 's/%//' || \
    df / | tail -1 | awk '{print $5}' | sed 's/%//'
}

# Get available space in MB
get_available_space() {
    df /var/lib/docker --output=avail 2>/dev/null | tail -1 | awk '{print int($1/1024)}' || \
    df / --output=avail | tail -1 | awk '{print int($1/1024)}'
}

# Get used space in MB
get_used_space() {
    df /var/lib/docker --output=used 2>/dev/null | tail -1 | awk '{print int($1/1024)}' || \
    df / --output=used | tail -1 | awk '{print int($1/1024)}'
}

# Send email alert if configured
send_alert() {
    local subject="$1"
    local message="$2"
    
    if [ -n "$EMAIL_ALERT" ] && command -v mail >/dev/null 2>&1; then
        echo -e "$message" | mail -s "$subject" "$EMAIL_ALERT"
        log "📧 Alert sent to $EMAIL_ALERT"
    fi
}

# Run cleanup script
run_cleanup() {
    local cleanup_type="$1"
    
    if [ -x "$CLEANUP_SCRIPT" ]; then
        log "${BLUE}🧹 Running $cleanup_type cleanup...${NC}"
        if "$CLEANUP_SCRIPT" --force; then
            log "${GREEN}✅ Cleanup completed successfully${NC}"
            return 0
        else
            log "${RED}❌ Cleanup failed${NC}"
            return 1
        fi
    else
        log "${YELLOW}⚠️  Cleanup script not found or not executable: $CLEANUP_SCRIPT${NC}"
        
        # Fallback to basic Docker cleanup
        log "${BLUE}🧹 Running basic Docker cleanup...${NC}"
        docker system prune -af --filter "until=24h" >/dev/null 2>&1 || true
        docker volume prune -f >/dev/null 2>&1 || true
        return 0
    fi
}

# Get Docker information
get_docker_info() {
    local info=""
    
    if command -v docker >/dev/null 2>&1 && docker info >/dev/null 2>&1; then
        local containers=$(docker ps -q | wc -l)
        local images=$(docker images -q | wc -l)
        local volumes=$(docker volume ls -q | wc -l)
        
        info="Docker: $containers containers, $images images, $volumes volumes"
    else
        info="Docker: Not available"
    fi
    
    echo "$info"
}

# Monitor disk usage and take action
monitor_disk() {
    local usage=$(get_disk_usage)
    local available=$(get_available_space)
    local used=$(get_used_space)
    local docker_info=$(get_docker_info)
    
    log "${BLUE}📊 Disk Monitor Check${NC}"
    log "Current usage: ${usage}% (${used}MB used, ${available}MB available)"
    log "$docker_info"
    
    if [ "$usage" -ge "$CRITICAL" ]; then
        log "${RED}🚨 CRITICAL: Disk usage at ${usage}% - Emergency cleanup required!${NC}"
        
        # Send critical alert
        send_alert "CRITICAL: Talaria Dashboard Server Disk Space" \
            "Disk usage has reached critical level: ${usage}%\n\nUsed: ${used}MB\nAvailable: ${available}MB\n\nEmergency cleanup has been triggered.\n\n$docker_info"
        
        # Run emergency cleanup
        if run_cleanup "emergency"; then
            local new_usage=$(get_disk_usage)
            local new_available=$(get_available_space)
            log "${GREEN}✅ Emergency cleanup completed. New usage: ${new_usage}% (${new_available}MB available)${NC}"
            
            # Send success notification
            send_alert "INFO: Talaria Dashboard Emergency Cleanup Completed" \
                "Emergency cleanup completed successfully.\n\nBefore: ${usage}% (${available}MB available)\nAfter: ${new_usage}% (${new_available}MB available)"
        else
            log "${RED}❌ Emergency cleanup failed! Manual intervention required!${NC}"
            
            # Send failure alert
            send_alert "URGENT: Talaria Dashboard Emergency Cleanup Failed" \
                "Emergency cleanup failed! Manual intervention required immediately.\n\nCurrent usage: ${usage}%\nAvailable: ${available}MB\n\nPlease log in to the server and free up disk space manually."
        fi
        
    elif [ "$usage" -ge "$THRESHOLD" ]; then
        log "${YELLOW}⚠️  WARNING: Disk usage at ${usage}% - Preventive cleanup triggered${NC}"
        
        # Send warning alert
        send_alert "WARNING: Talaria Dashboard Server Disk Space" \
            "Disk usage has reached warning level: ${usage}%\n\nUsed: ${used}MB\nAvailable: ${available}MB\n\nPreventive cleanup has been triggered.\n\n$docker_info"
        
        # Run preventive cleanup
        if run_cleanup "preventive"; then
            local new_usage=$(get_disk_usage)
            local new_available=$(get_available_space)
            log "${GREEN}✅ Preventive cleanup completed. New usage: ${new_usage}% (${new_available}MB available)${NC}"
        else
            log "${YELLOW}⚠️  Preventive cleanup had issues, but system is still functional${NC}"
        fi
        
    else
        log "${GREEN}✅ Disk usage normal: ${usage}% (${available}MB available)${NC}"
    fi
    
    # Log Docker stats for monitoring
    log "---"
}

# Main execution
main() {
    # Create log directory if it doesn't exist
    mkdir -p "$(dirname "$LOG_FILE")"
    
    # Validate thresholds
    if [ "$THRESHOLD" -ge "$CRITICAL" ]; then
        log "${RED}ERROR: Warning threshold ($THRESHOLD%) must be less than critical threshold ($CRITICAL%)${NC}"
        exit 1
    fi
    
    # Run monitoring
    monitor_disk
}

# Execute main function
main "$@"