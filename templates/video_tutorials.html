{% extends "base.html" %} {% block title %}Video Tutorials - Talaria Dashboard{%
endblock %} {% block content %}
<div
  class="min-h-screen bg-gradient-to-br from-orange-50 to-red-100 dark:from-gray-900 dark:to-gray-800"
>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Enhanced <PERSON>er -->
    <div class="text-center mb-12">
      <div
        class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-orange-500 to-red-600 rounded-full mb-6"
      >
        <i class="fas fa-video text-white text-3xl"></i>
      </div>
      <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
        Video Tutorials
      </h1>
      <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
        Master Talaria Dashboard with our comprehensive video guides. Learn at
        your own pace with step-by-step tutorials covering everything from basic
        operations to advanced automation.
      </p>
    </div>

    <!-- Search and Filter Bar -->
    <div class="mb-8">
      <div class="max-w-2xl mx-auto">
        <div class="relative">
          <input
            type="text"
            id="tutorialSearch"
            class="w-full px-6 py-4 text-lg border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:ring-4 focus:ring-orange-500/20 focus:border-orange-500 dark:bg-gray-800 dark:text-white shadow-lg transition-all duration-200"
            placeholder="Search tutorials... (e.g., 'inventory search', 'asana setup', 'email automation')"
          />
          <div class="absolute right-4 top-1/2 transform -translate-y-1/2">
            <i class="fas fa-search text-gray-400 text-xl"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- Difficulty Filter -->
    <div class="mb-8">
      <div class="flex flex-wrap justify-center gap-3">
        <button
          class="difficulty-filter px-4 py-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full border border-gray-200 dark:border-gray-600 hover:bg-orange-50 dark:hover:bg-gray-700 transition-colors"
          data-difficulty="all"
        >
          <i class="fas fa-list mr-2"></i>All Levels
        </button>
        <button
          class="difficulty-filter px-4 py-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full border border-gray-200 dark:border-gray-600 hover:bg-orange-50 dark:hover:bg-gray-700 transition-colors"
          data-difficulty="Beginner"
        >
          <i class="fas fa-seedling mr-2"></i>Beginner
        </button>
        <button
          class="difficulty-filter px-4 py-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full border border-gray-200 dark:border-gray-600 hover:bg-orange-50 dark:hover:bg-gray-700 transition-colors"
          data-difficulty="Intermediate"
        >
          <i class="fas fa-chart-line mr-2"></i>Intermediate
        </button>
        <button
          class="difficulty-filter px-4 py-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full border border-gray-200 dark:border-gray-600 hover:bg-orange-50 dark:hover:bg-gray-700 transition-colors"
          data-difficulty="Advanced"
        >
          <i class="fas fa-rocket mr-2"></i>Advanced
        </button>
      </div>
    </div>

    <!-- Tutorial Categories -->
    <div class="space-y-12" id="tutorialContainer">
      {% for category_id, category in tutorials.items() %}
      <div class="tutorial-category" data-category="{{ category_id }}">
        <!-- Category Header -->
        <div
          class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden border border-gray-100 dark:border-gray-700"
        >
          <div class="bg-gradient-to-r {{ category.color }} px-8 py-6">
            <div class="flex items-center">
              <div
                class="w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center mr-6"
              >
                <i class="{{ category.icon }} text-white text-2xl"></i>
              </div>
              <div>
                <h2 class="text-3xl font-bold text-white mb-2">
                  {{ category.title }}
                </h2>
                <p class="text-white/90 text-lg">{{ category.description }}</p>
              </div>
            </div>
          </div>

          <!-- Videos Grid -->
          <div class="p-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {% for video in category.videos %}
              <div
                class="tutorial-video bg-gray-50 dark:bg-gray-700 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer"
                data-video-id="{{ video.id }}"
                data-difficulty="{{ video.difficulty }}"
                data-tags="{{ video.tags|join(',') }}"
              >
                <!-- Video Thumbnail -->
                <div class="relative">
                  <img
                    src="{{ video.thumbnail }}"
                    alt="{{ video.title }}"
                    class="w-full h-48 object-cover"
                    onerror="this.src='/static/img/video-placeholder.jpg'"
                  />
                  <div
                    class="absolute inset-0 bg-black/40 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity"
                  >
                    <div
                      class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm"
                    >
                      <i class="fas fa-play text-white text-xl ml-1"></i>
                    </div>
                  </div>
                  <!-- Duration Badge -->
                  <div
                    class="absolute bottom-3 right-3 bg-black/70 text-white px-2 py-1 rounded text-sm"
                  >
                    {{ video.duration }}
                  </div>
                  <!-- Difficulty Badge -->
                  <div
                    class="absolute top-3 left-3 px-2 py-1 rounded text-xs font-semibold {% if video.difficulty == 'Beginner' %}bg-green-500 text-white{% endif %} {% if video.difficulty == 'Intermediate' %}bg-yellow-500 text-white{% endif %} {% if video.difficulty == 'Advanced' %}bg-red-500 text-white{% endif %}"
                  >
                    {{ video.difficulty }}
                  </div>
                </div>

                <!-- Video Info -->
                <div class="p-6">
                  <h3
                    class="text-lg font-semibold text-gray-900 dark:text-white mb-2"
                  >
                    {{ video.title }}
                  </h3>
                  <p class="text-gray-600 dark:text-gray-400 text-sm mb-4">
                    {{ video.description }}
                  </p>

                  <!-- Tags -->
                  <div class="flex flex-wrap gap-2 mb-4">
                    {% for tag in video.tags %}
                    <span
                      class="px-2 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded text-xs"
                    >
                      {{ tag }}
                    </span>
                    {% endfor %}
                  </div>

                  <!-- Action Buttons -->
                  <div class="flex gap-2">
                    <button
                      class="flex-1 bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg transition-colors"
                      onclick="playVideo('{{ video.id }}', '{{ video.video_url }}', '{{ video.title }}')"
                    >
                      <i class="fas fa-play mr-2"></i>Watch
                    </button>
                    <button
                      class="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
                      onclick="showTranscript('{{ video.transcript_url }}', '{{ video.title }}')"
                    >
                      <i class="fas fa-file-text"></i>
                    </button>
                  </div>
                </div>
              </div>
              {% endfor %}
            </div>
          </div>
        </div>
      </div>
      {% endfor %}
    </div>

    <!-- No Results Message -->
    <div id="noResults" class="hidden text-center py-16">
      <div
        class="inline-flex items-center justify-center w-20 h-20 bg-gray-100 dark:bg-gray-700 rounded-full mb-6"
      >
        <i class="fas fa-search text-gray-400 text-2xl"></i>
      </div>
      <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
        No tutorials found
      </h3>
      <p class="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
        Try adjusting your search terms or difficulty level to find relevant
        tutorials.
      </p>
    </div>

    <!-- Video Player Modal -->
    <div
      id="videoModal"
      class="fixed inset-0 bg-black/80 z-50 hidden flex items-center justify-center p-4"
    >
      <div
        class="bg-white dark:bg-gray-800 rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
      >
        <!-- Modal Header -->
        <div
          class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-600"
        >
          <h3
            id="videoTitle"
            class="text-xl font-semibold text-gray-900 dark:text-white"
          ></h3>
          <button
            onclick="closeVideoModal()"
            class="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
          >
            <i class="fas fa-times text-xl"></i>
          </button>
        </div>

        <!-- Video Player -->
        <div class="p-6">
          <video id="videoPlayer" class="w-full rounded-lg" controls>
            <source src="" type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        </div>
      </div>
    </div>

    <!-- Transcript Modal -->
    <div
      id="transcriptModal"
      class="fixed inset-0 bg-black/80 z-50 hidden flex items-center justify-center p-4"
    >
      <div
        class="bg-white dark:bg-gray-800 rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden"
      >
        <!-- Modal Header -->
        <div
          class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-600"
        >
          <h3
            id="transcriptTitle"
            class="text-xl font-semibold text-gray-900 dark:text-white"
          >
            Transcript
          </h3>
          <button
            onclick="closeTranscriptModal()"
            class="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
          >
            <i class="fas fa-times text-xl"></i>
          </button>
        </div>

        <!-- Transcript Content -->
        <div class="p-6 max-h-96 overflow-y-auto">
          <div
            id="transcriptContent"
            class="prose prose-gray dark:prose-invert max-w-none"
          >
            Loading transcript...
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    initializeTutorials();
    setupSearch();
    setupFilters();
  });

  function initializeTutorials() {
    // Add click handlers to video cards
    document.querySelectorAll(".tutorial-video").forEach((card) => {
      card.addEventListener("click", function () {
        const videoId = this.dataset.videoId;
        const videoUrl = this.querySelector("img")
          .src.replace("_thumb.jpg", ".mp4")
          .replace("/img/tutorials/", "/videos/tutorials/");
        const title = this.querySelector("h3").textContent;
        playVideo(videoId, videoUrl, title);
      });
    });

    // Set default filter
    document.querySelector('.difficulty-filter[data-difficulty="all"]').click();
  }

  function setupSearch() {
    const searchInput = document.getElementById("tutorialSearch");
    const tutorialVideos = document.querySelectorAll(".tutorial-video");
    const tutorialCategories = document.querySelectorAll(".tutorial-category");
    const noResults = document.getElementById("noResults");

    searchInput.addEventListener("input", function (e) {
      const searchTerm = e.target.value.toLowerCase().trim();
      let visibleCount = 0;

      if (searchTerm === "") {
        // Show all videos and categories
        tutorialVideos.forEach((video) => {
          video.style.display = "block";
          visibleCount++;
        });
        tutorialCategories.forEach((category) => {
          category.style.display = "block";
        });
        noResults.classList.add("hidden");
        return;
      }

      // Search through tutorial videos
      tutorialVideos.forEach((video) => {
        const title = video.querySelector("h3").textContent.toLowerCase();
        const description = video.querySelector("p").textContent.toLowerCase();
        const tags = video.dataset.tags.toLowerCase();

        if (
          title.includes(searchTerm) ||
          description.includes(searchTerm) ||
          tags.includes(searchTerm)
        ) {
          video.style.display = "block";
          visibleCount++;
        } else {
          video.style.display = "none";
        }
      });

      // Hide categories that have no visible videos
      tutorialCategories.forEach((category) => {
        const visibleVideos = category.querySelectorAll(
          '.tutorial-video[style*="block"]'
        );
        if (visibleVideos.length === 0) {
          category.style.display = "none";
        } else {
          category.style.display = "block";
        }
      });

      // Show no results message if needed
      if (visibleCount === 0) {
        noResults.classList.remove("hidden");
      } else {
        noResults.classList.add("hidden");
      }
    });
  }

  function setupFilters() {
    const filterButtons = document.querySelectorAll(".difficulty-filter");
    const tutorialVideos = document.querySelectorAll(".tutorial-video");
    const tutorialCategories = document.querySelectorAll(".tutorial-category");

    filterButtons.forEach((button) => {
      button.addEventListener("click", () => {
        const difficulty = button.dataset.difficulty;

        // Update active filter styling
        filterButtons.forEach((btn) => {
          btn.classList.remove("bg-orange-500", "text-white");
          btn.classList.add(
            "bg-white",
            "dark:bg-gray-800",
            "text-gray-700",
            "dark:text-gray-300"
          );
        });
        button.classList.remove(
          "bg-white",
          "dark:bg-gray-800",
          "text-gray-700",
          "dark:text-gray-300"
        );
        button.classList.add("bg-orange-500", "text-white");

        // Clear search
        document.getElementById("tutorialSearch").value = "";
        document.getElementById("noResults").classList.add("hidden");

        // Filter videos by difficulty
        if (difficulty === "all") {
          tutorialVideos.forEach((video) => {
            video.style.display = "block";
          });
          tutorialCategories.forEach((category) => {
            category.style.display = "block";
          });
        } else {
          tutorialVideos.forEach((video) => {
            if (video.dataset.difficulty === difficulty) {
              video.style.display = "block";
            } else {
              video.style.display = "none";
            }
          });

          // Hide categories with no visible videos
          tutorialCategories.forEach((category) => {
            const visibleVideos = category.querySelectorAll(
              '.tutorial-video[style*="block"]'
            );
            if (visibleVideos.length === 0) {
              category.style.display = "none";
            } else {
              category.style.display = "block";
            }
          });
        }
      });
    });
  }

  function playVideo(videoId, videoUrl, title) {
    const modal = document.getElementById("videoModal");
    const player = document.getElementById("videoPlayer");
    const titleElement = document.getElementById("videoTitle");

    titleElement.textContent = title;
    player.src = videoUrl;
    modal.classList.remove("hidden");

    // Play video when modal opens
    player.play().catch((e) => {
      console.log("Video autoplay prevented:", e);
      // Show a message or handle the autoplay prevention
      showNotification("Click the play button to start the video", "info");
    });
  }

  function closeVideoModal() {
    const modal = document.getElementById("videoModal");
    const player = document.getElementById("videoPlayer");

    player.pause();
    player.src = "";
    modal.classList.add("hidden");
  }

  function showTranscript(transcriptUrl, title) {
    const modal = document.getElementById("transcriptModal");
    const titleElement = document.getElementById("transcriptTitle");
    const contentElement = document.getElementById("transcriptContent");

    titleElement.textContent = `${title} - Transcript`;
    contentElement.innerHTML = "Loading transcript...";
    modal.classList.remove("hidden");

    // Fetch transcript content
    fetch(transcriptUrl)
      .then((response) => {
        if (!response.ok) {
          throw new Error("Transcript not available");
        }
        return response.text();
      })
      .then((text) => {
        contentElement.innerHTML = `<pre class="whitespace-pre-wrap">${text}</pre>`;
      })
      .catch((error) => {
        contentElement.innerHTML = `
          <div class="text-center py-8">
            <i class="fas fa-file-text text-gray-400 text-4xl mb-4"></i>
            <p class="text-gray-600 dark:text-gray-400">Transcript not available yet.</p>
            <p class="text-sm text-gray-500 dark:text-gray-500 mt-2">We're working on adding transcripts for all videos.</p>
          </div>
        `;
      });
  }

  function closeTranscriptModal() {
    const modal = document.getElementById("transcriptModal");
    modal.classList.add("hidden");
  }

  function showNotification(message, type = "info") {
    // Create a simple notification
    const notification = document.createElement("div");
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg text-white z-50 transition-opacity duration-300 ${
      type === "error"
        ? "bg-red-500"
        : type === "success"
        ? "bg-green-500"
        : "bg-blue-500"
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
      notification.style.opacity = "0";
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }

  // Close modals when clicking outside
  document.addEventListener("click", function (e) {
    if (e.target.id === "videoModal") {
      closeVideoModal();
    }
    if (e.target.id === "transcriptModal") {
      closeTranscriptModal();
    }
  });

  // Keyboard shortcuts
  document.addEventListener("keydown", function (e) {
    if (e.key === "Escape") {
      closeVideoModal();
      closeTranscriptModal();
    }
  });
</script>

<style>
  /* Enhanced animations and transitions */
  .tutorial-video {
    transition: all 0.3s ease;
  }

  .tutorial-video:hover {
    transform: translateY(-4px);
  }

  .difficulty-filter {
    transition: all 0.2s ease;
  }

  .difficulty-filter:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  /* Video player styling */
  #videoPlayer {
    max-height: 60vh;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar for transcript */
  #transcriptContent {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e0 #f7fafc;
  }

  #transcriptContent::-webkit-scrollbar {
    width: 6px;
  }

  #transcriptContent::-webkit-scrollbar-track {
    background: #f7fafc;
    border-radius: 3px;
  }

  #transcriptContent::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 3px;
  }

  #transcriptContent::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
  }
</style>
{% endblock %}
