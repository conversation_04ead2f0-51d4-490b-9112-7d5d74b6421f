{% extends "base.html" %} {% block title %}Support - Talaria Dashboard{%
endblock %} {% block content %}
<div
  class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800"
>
  <div class="container mx-auto px-4 py-8">
    <div class="max-w-6xl mx-auto">
      <!-- Enhanced Header -->
      <div class="text-center mb-12">
        <div
          class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full mb-6"
        >
          <i class="fas fa-headset text-white text-3xl"></i>
        </div>
        <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
          Support Center
        </h1>
        <p class="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
          Welcome to Talaria Dashboard Support. We're here to help you get the
          most out of your wafer management system with comprehensive resources
          and expert assistance.
        </p>
      </div>

      <!-- Quick Help Section -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Documentation -->
        <a
          href="{{ url_for('documentation') }}"
          class="group bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 hover:shadow-xl transition-all"
        >
          <div class="flex items-center space-x-4">
            <div
              class="p-3 bg-blue-100 dark:bg-blue-900 rounded-lg group-hover:scale-110 transition-transform"
            >
              <i
                class="fas fa-book text-blue-600 dark:text-blue-400 text-xl"
              ></i>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                Documentation
              </h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">
                Browse user guides and tutorials
              </p>
            </div>
          </div>
        </a>

        <!-- FAQs -->
        <a
          href="{{ url_for('faqs') }}"
          class="group bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 hover:shadow-xl transition-all"
        >
          <div class="flex items-center space-x-4">
            <div
              class="p-3 bg-green-100 dark:bg-green-900 rounded-lg group-hover:scale-110 transition-transform"
            >
              <i
                class="fas fa-question-circle text-green-600 dark:text-green-400 text-xl"
              ></i>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                FAQs
              </h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">
                Find answers to common questions
              </p>
            </div>
          </div>
        </a>

        <!-- Live Chat -->
        <button
          onclick="openLiveChat()"
          class="group bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 hover:shadow-xl transition-all"
        >
          <div class="flex items-center space-x-4">
            <div
              class="p-3 bg-purple-100 dark:bg-purple-900 rounded-lg group-hover:scale-110 transition-transform"
            >
              <i
                class="fas fa-comments text-purple-600 dark:text-purple-400 text-xl"
              ></i>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                Live Chat
              </h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">
                Chat with support team
              </p>
            </div>
          </div>
        </button>
      </div>

      <!-- Quick Links for Common Issues -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          Quick Links
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          Common issues or suggestions? Click one of these quick links to
          pre-fill the form:
        </p>

        <div
          class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
          id="quick-issues-container"
        >
          <!-- Quick links will be populated by JavaScript -->
          <div
            class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors"
          >
            <div class="flex items-center mb-2">
              <div
                class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mr-3"
              >
                <i
                  class="fas fa-spinner fa-spin text-blue-600 dark:text-blue-400"
                ></i>
              </div>
              <h3 class="font-medium">Loading quick links...</h3>
            </div>
          </div>
        </div>
      </div>

      <!-- Contact Form -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
          Contact Support
        </h2>
        <form id="support-form" class="space-y-6">
          <!-- Issue Type -->
          <div>
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
              >Issue Type</label
            >
            <select
              name="issueType"
              class="form-select w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              title="Issue Type"
            >
              <option value="">Select an issue type</option>
              <option value="technical">Technical Issue</option>
              <option value="account">Account Related</option>
              <option value="feature">Feature Request</option>
              <option value="other">Other</option>
            </select>
          </div>

          <!-- Priority -->
          <div>
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
              >Priority</label
            >
            <div class="flex space-x-4">
              <label class="inline-flex items-center">
                <input
                  type="radio"
                  name="priority"
                  value="low"
                  class="form-radio text-blue-600"
                />
                <span class="ml-2 text-gray-700 dark:text-gray-300">Low</span>
              </label>
              <label class="inline-flex items-center">
                <input
                  type="radio"
                  name="priority"
                  value="medium"
                  class="form-radio text-blue-600"
                />
                <span class="ml-2 text-gray-700 dark:text-gray-300"
                  >Medium</span
                >
              </label>
              <label class="inline-flex items-center">
                <input
                  type="radio"
                  name="priority"
                  value="high"
                  class="form-radio text-blue-600"
                />
                <span class="ml-2 text-gray-700 dark:text-gray-300">High</span>
              </label>
            </div>
          </div>

          <!-- Subject -->
          <div>
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
              >Subject</label
            >
            <input
              type="text"
              name="subject"
              class="form-input w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              placeholder="Brief description of the issue"
            />
          </div>

          <!-- Message -->
          <div>
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
              >Message</label
            >
            <textarea
              name="message"
              rows="4"
              class="form-textarea w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              placeholder="Detailed description of your issue"
            ></textarea>
          </div>

          <!-- Attachments -->
          <div>
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
              >Attachments</label
            >
            <div class="flex items-center">
              <input
                type="file"
                name="attachments"
                multiple
                class="hidden"
                id="file-upload"
              />
              <label
                for="file-upload"
                class="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600"
              >
                <i class="fas fa-paperclip mr-2"></i>
                Add Files
              </label>
              <span
                id="file-list"
                class="ml-3 text-sm text-gray-500 dark:text-gray-400"
              ></span>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="flex justify-end">
            <button
              type="submit"
              class="px-6 py-2.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <i class="fas fa-paper-plane mr-2"></i>
              Submit Ticket
            </button>
          </div>
        </form>
      </div>

      <!-- Support Status -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          System Status
        </h2>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4" id="system-status">
          <!-- Status will be populated by JavaScript -->
        </div>
      </div>
    </div>
  </div>

  <!-- Live Chat Modal -->
  <div id="chat-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
    <div
      class="absolute bottom-0 right-0 m-6 w-96 bg-white dark:bg-gray-800 rounded-lg shadow-xl"
    >
      <!-- Chat Header -->
      <div
        class="flex items-center justify-between p-4 border-b dark:border-gray-700"
      >
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
          Live Support
        </h3>
        <button
          onclick="closeLiveChat()"
          class="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
          title="Close"
        >
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- Chat Messages -->
      <div id="chat-messages" class="h-96 overflow-y-auto p-4 space-y-4">
        <!-- Messages will be populated by JavaScript -->
      </div>

      <!-- Chat Input -->
      <div class="p-4 border-t dark:border-gray-700">
        <form id="chat-form" class="flex items-center space-x-2">
          <input
            type="text"
            id="chat-input"
            class="flex-1 form-input rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            placeholder="Type your message..."
          />
          <button
            type="submit"
            class="p-2 text-blue-600 hover:text-blue-700 dark:text-blue-400"
          >
            <i class="fas fa-paper-plane" title="Send"></i>
          </button>
        </form>
      </div>
    </div>
  </div>
  {% endblock %} {% block extra_js %}
  <script>
    document.addEventListener("DOMContentLoaded", function () {
      initializeSupport();
      loadSystemStatus();
      setupFileUpload();
      loadQuickIssues();
      setupFormValidation();
    });

    function initializeSupport() {
      // Support form submission
      const supportForm = document.getElementById("support-form");
      supportForm.addEventListener("submit", async (e) => {
        e.preventDefault();

        // Validate form before submission
        if (!validateSupportForm()) {
          return;
        }

        // Show loading indicator
        const submitBtn = supportForm.querySelector('button[type="submit"]');
        const originalBtnText = submitBtn.innerHTML;
        submitBtn.innerHTML =
          '<i class="fas fa-spinner fa-spin mr-2"></i> Submitting...';
        submitBtn.disabled = true;

        await submitSupportTicket();

        // Restore button state
        submitBtn.innerHTML = originalBtnText;
        submitBtn.disabled = false;
      });

      // Chat form submission
      const chatForm = document.getElementById("chat-form");
      chatForm.addEventListener("submit", (e) => {
        e.preventDefault();
        sendChatMessage();
      });
    }

    function validateSupportForm() {
      const form = document.getElementById("support-form");
      const issueType = form.querySelector('select[name="issueType"]').value;
      const priority = form.querySelector('input[name="priority"]:checked');
      const subject = form.querySelector('input[name="subject"]').value.trim();
      const message = form
        .querySelector('textarea[name="message"]')
        .value.trim();

      let isValid = true;

      // Reset previous error messages
      form.querySelectorAll(".error-message").forEach((el) => el.remove());

      // Validate issue type
      if (!issueType) {
        showFieldError(
          'select[name="issueType"]',
          "Please select an issue type"
        );
        isValid = false;
      }

      // Validate priority
      if (!priority) {
        showFieldError(
          'input[name="priority"]',
          "Please select a priority level"
        );
        isValid = false;
      }

      // Validate subject
      if (!subject) {
        showFieldError('input[name="subject"]', "Please enter a subject");
        isValid = false;
      }

      // Validate message
      if (!message) {
        showFieldError('textarea[name="message"]', "Please enter a message");
        isValid = false;
      }

      return isValid;
    }

    function showFieldError(selector, message) {
      const field = document.querySelector(selector);
      const errorDiv = document.createElement("div");
      errorDiv.className = "text-red-500 text-sm mt-1 error-message";
      errorDiv.textContent = message;

      // Find the parent container for the field
      const fieldContainer = field.closest("div");
      fieldContainer.appendChild(errorDiv);

      // Highlight the field
      if (
        field.tagName === "SELECT" ||
        field.tagName === "INPUT" ||
        field.tagName === "TEXTAREA"
      ) {
        field.classList.add("border-red-500");

        // Remove highlight when field is interacted with
        field.addEventListener(
          "input",
          function () {
            this.classList.remove("border-red-500");
            const error = this.closest("div").querySelector(".error-message");
            if (error) error.remove();
          },
          { once: true }
        );
      }
    }

    async function submitSupportTicket() {
      try {
        const formData = new FormData(document.getElementById("support-form"));

        const response = await fetch("/api/support/ticket", {
          method: "POST",
          headers: {
            "X-CSRFToken": "{{ csrf_token }}",
          },
          body: formData,
        });

        if (response.ok) {
          const result = await response.json();
          showToast("Support ticket submitted successfully", "success");
          document.getElementById("support-form").reset();

          // Show success message with ticket ID
          Swal.fire({
            title: "Ticket Submitted!",
            text: `Your support ticket #${result.ticket_id} has been submitted successfully. Our team will review it shortly.`,
            icon: "success",
            confirmButtonText: "OK",
          });
        } else {
          const errorData = await response.json();
          throw new Error(errorData.message || "Failed to submit ticket");
        }
      } catch (error) {
        console.error("Error submitting ticket:", error);
        showToast("Error submitting ticket: " + error.message, "error");

        Swal.fire({
          title: "Submission Failed",
          text:
            error.message ||
            "There was an error submitting your ticket. Please try again.",
          icon: "error",
          confirmButtonText: "OK",
        });
      }
    }

    function setupFileUpload() {
      const fileUpload = document.getElementById("file-upload");
      const fileList = document.getElementById("file-list");

      fileUpload.addEventListener("change", (e) => {
        const files = Array.from(e.target.files);
        fileList.textContent = files.length
          ? `${files.length} file(s) selected`
          : "";
      });
    }

    function setupFormValidation() {
      // Add validation classes to form fields
      const form = document.getElementById("support-form");

      // Add required attribute to important fields
      form.querySelector('select[name="issueType"]').required = true;
      form.querySelector('input[name="subject"]').required = true;
      form.querySelector('textarea[name="message"]').required = true;

      // Add validation styling
      const inputs = form.querySelectorAll(
        'input[type="text"], textarea, select'
      );
      inputs.forEach((input) => {
        input.addEventListener("focus", function () {
          this.classList.add("border-blue-500");
        });

        input.addEventListener("blur", function () {
          this.classList.remove("border-blue-500");
          if (!this.value && this.required) {
            this.classList.add("border-red-500");
          } else {
            this.classList.remove("border-red-500");
          }
        });
      });
    }

    async function loadQuickIssues() {
      try {
        const container = document.getElementById("quick-issues-container");

        // First try to fetch from API
        try {
          const response = await fetch("/api/support/quick-issues");
          const data = await response.json();

          if (
            data.success &&
            data.quick_issues &&
            data.quick_issues.length > 0
          ) {
            renderQuickIssues(data.quick_issues, container);
            return;
          }
        } catch (apiError) {
          console.warn(
            "Could not load quick issues from API, using fallback data",
            apiError
          );
        }

        // Fallback to hardcoded issues if API fails
        const fallbackIssues = [
          {
            id: "email_preview",
            title: "Email Preview Issues",
            description: "Problems with email preview not loading or sending",
            issue_type: "technical",
            priority: "high",
          },
          {
            id: "asana_integration",
            title: "Asana Integration",
            description: "Issues with Asana task linking or data extraction",
            issue_type: "technical",
            priority: "high",
          },
          {
            id: "inventory",
            title: "Inventory Management",
            description:
              "Issues with inventory search, tracking, or bulk operations",
            issue_type: "technical",
            priority: "medium",
          },
          {
            id: "rfq_automation",
            title: "RFQ Automation",
            description: "Problems with RFQ email automation or templates",
            issue_type: "technical",
            priority: "medium",
          },
          {
            id: "label_generation",
            title: "Label Generation",
            description: "Issues with generating labels or packing slips",
            issue_type: "technical",
            priority: "high",
          },
          {
            id: "feature",
            title: "Feature Request",
            description:
              "Suggest a new feature or improvement for Talaria Dashboard",
            issue_type: "feature",
            priority: "medium",
          },
          {
            id: "bug",
            title: "Report a Bug",
            description: "Report a software bug or unexpected behavior",
            issue_type: "technical",
            priority: "high",
          },
        ];

        renderQuickIssues(fallbackIssues, container);
      } catch (error) {
        console.error("Error loading quick issues:", error);
        document.getElementById("quick-issues-container").innerHTML = `
        <div class="p-4 border border-red-200 rounded-lg">
          <p class="text-red-500">Error loading quick links. Please fill out the form manually.</p>
        </div>
      `;
      }
    }

    function renderQuickIssues(issues, container) {
      container.innerHTML = "";

      issues.forEach((issue) => {
        const issueElement = document.createElement("div");
        issueElement.className =
          "p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors";
        issueElement.innerHTML = `
        <div class="flex items-center mb-2">
          <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mr-3">
            <i class="fas fa-${getIconForIssueType(
              issue.issue_type
            )} text-blue-600 dark:text-blue-400"></i>
          </div>
          <h3 class="font-medium">${issue.title}</h3>
        </div>
        <p class="text-sm text-gray-600 dark:text-gray-400">${
          issue.description
        }</p>
      `;

        // Add click handler to pre-fill the form
        issueElement.addEventListener("click", () => {
          prefillSupportForm(issue);
        });

        container.appendChild(issueElement);
      });
    }

    function getIconForIssueType(type) {
      const icons = {
        technical: "wrench",
        account: "user-circle",
        feature: "lightbulb",
        other: "question-circle",
      };

      return icons[type] || "info-circle";
    }

    function prefillSupportForm(issue) {
      const form = document.getElementById("support-form");

      // Set issue type
      const issueTypeSelect = form.querySelector('select[name="issueType"]');
      issueTypeSelect.value = issue.issue_type;

      // Set priority
      const priorityRadio = form.querySelector(
        `input[name="priority"][value="${issue.priority}"]`
      );
      if (priorityRadio) {
        priorityRadio.checked = true;
      }

      // Set subject
      form.querySelector('input[name="subject"]').value = issue.title;

      // Focus on message textarea for user to fill in details
      const messageTextarea = form.querySelector('textarea[name="message"]');
      messageTextarea.value = `I'm experiencing an issue with ${issue.title.toLowerCase()}:\n\n`;
      messageTextarea.focus();

      // Scroll to form
      form.scrollIntoView({ behavior: "smooth", block: "start" });
    }

    async function loadSystemStatus() {
      try {
        const response = await fetch("/api/system/status");
        const data = await response.json();

        const statusContainer = document.getElementById("system-status");

        // Create services array if it doesn't exist in the response
        const services = data.services || [
          {
            name: "Database",
            status: data.storage?.database ? "Operational" : "Degraded",
          },
          {
            name: "Storage",
            status: data.storage?.local_storage ? "Operational" : "Degraded",
          },
          { name: "API", status: "Operational" },
          { name: "Web Interface", status: "Operational" },
        ];

        statusContainer.innerHTML = services
          .map(
            (service) => `
          <div class="text-center">
            <div class="inline-flex items-center px-3 py-1 rounded-full text-sm
                     ${
                       service.status === "Operational"
                         ? "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200"
                         : "bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200"
                     }">
                <span class="w-2 h-2 rounded-full ${
                  service.status === "Operational"
                    ? "bg-green-500"
                    : "bg-yellow-500"
                } mr-2"></span>
                ${service.status}
            </div>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">${
              service.name
            }</p>
          </div>
        `
          )
          .join("");
      } catch (error) {
        console.error("Error loading system status:", error);
        document.getElementById("system-status").innerHTML = `
        <div class="col-span-4 text-center text-gray-500">
          <i class="fas fa-exclamation-triangle mr-2"></i>
          Unable to load system status
        </div>
      `;
      }
    }

    // Live Chat Functions
    function openLiveChat() {
      document.getElementById("chat-modal").classList.remove("hidden");
      // Initialize chat connection here

      // Add initial welcome message
      setTimeout(() => {
        addChatMessage(
          "system",
          "Welcome to Talaria Dashboard support! How can we help you today?"
        );
      }, 500);
    }

    function closeLiveChat() {
      document.getElementById("chat-modal").classList.add("hidden");
      // Clean up chat connection here
    }

    function sendChatMessage() {
      const input = document.getElementById("chat-input");
      const message = input.value.trim();

      if (message) {
        addChatMessage("user", message);
        input.value = "";

        // Show typing indicator
        const messagesContainer = document.getElementById("chat-messages");
        const typingIndicator = document.createElement("div");
        typingIndicator.className = "flex justify-start typing-indicator";
        typingIndicator.innerHTML = `
        <div class="bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white rounded-lg px-4 py-2 max-w-xs">
          <div class="flex space-x-1">
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
          </div>
        </div>
      `;
        messagesContainer.appendChild(typingIndicator);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;

        // Simulate response after delay
        setTimeout(() => {
          // Remove typing indicator
          document.querySelector(".typing-indicator").remove();

          // Add response message
          addChatMessage(
            "system",
            "Thank you for your message. For faster assistance, please submit a support ticket using the form below."
          );
        }, 1500);
      }
    }

    function addChatMessage(sender, message) {
      const messagesContainer = document.getElementById("chat-messages");
      const messageElement = document.createElement("div");
      messageElement.className = `flex ${
        sender === "user" ? "justify-end" : "justify-start"
      }`;

      messageElement.innerHTML = `
      <div class="${
        sender === "user"
          ? "bg-blue-600 text-white"
          : "bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white"
      }
                rounded-lg px-4 py-2 max-w-xs">
          ${message}
      </div>
    `;

      messagesContainer.appendChild(messageElement);
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    function showToast(message, type) {
      const colors = {
        success: "bg-green-500",
        error: "bg-red-500",
        info: "bg-blue-500",
      };

      const toast = document.createElement("div");
      toast.className = `fixed bottom-4 right-4 px-6 py-3 rounded-lg text-white ${colors[type]} transition-opacity duration-300 z-50`;
      toast.textContent = message;

      document.body.appendChild(toast);

      setTimeout(() => {
        toast.style.opacity = "0";
        setTimeout(() => toast.remove(), 300);
      }, 3000);
    }
  </script>

  <style>
    /* Typing indicator animation */
    .typing-dot {
      width: 8px;
      height: 8px;
      background-color: #888;
      border-radius: 50%;
      animation: typing-animation 1s infinite ease-in-out;
    }

    .typing-dot:nth-child(1) {
      animation-delay: 0s;
    }

    .typing-dot:nth-child(2) {
      animation-delay: 0.2s;
    }

    .typing-dot:nth-child(3) {
      animation-delay: 0.4s;
    }

    @keyframes typing-animation {
      0%,
      100% {
        transform: translateY(0);
      }
      50% {
        transform: translateY(-5px);
      }
    }
  </style>
  {% endblock %}
</div>
