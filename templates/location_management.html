{% extends "base.html" %} {% block title %}Location Management{% endblock %} {%
block content %}
<div class="container mx-auto px-4 py-8">
  <!-- Header Section -->
  <div class="mb-6 flex justify-between items-center">
    <div>
      <h1 class="text-2xl font-bold text-gray-800 dark:text-white">
        Location Management
      </h1>
      <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
        Manage wafer locations and shipping details
      </p>
    </div>

    <div class="flex space-x-2">
      <button
        onclick="exportLocations()"
        id="exportButton"
        class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out flex items-center"
      >
        <i class="fas fa-download mr-2"></i>Export
      </button>
    </div>
  </div>

  <!-- Search and Filter Section -->
  <div class="relative mb-6">
    <label
      for="location-search"
      class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
    >
      Search Locations
    </label>
    <div class="relative">
      <input
        type="text"
        id="location-search"
        placeholder="Search by name, address, or contact person..."
        class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
      />
      <button
        id="search-clear"
        class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 hidden"
        type="button"
        title="Clear Search"
      >
        <i class="fas fa-times"></i>
      </button>
    </div>
  </div>

  <!-- Location Form -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
    <form id="locationForm" class="space-y-6">
      <!-- CSRF Token -->
      <input type="hidden" name="csrf_token" value="{{ csrf_token }}" />

      <!-- Toggle for Add New Location -->
      <div class="flex items-center space-x-4 mb-4">
        <label class="flex items-center">
          <input
            type="checkbox"
            id="add-new-location-toggle"
            class="form-checkbox h-4 w-4 text-blue-600"
          />
          <span
            class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300"
            >Add New Location</span
          >
        </label>
        <div id="form-status" class="text-sm"></div>
      </div>

      <!-- Row 1 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
          <label
            for="location-name-dropdown"
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
          >
            Location Label
            <span class="text-red-500">*</span>
          </label>
          <div class="relative">
            <!-- Dropdown for existing locations -->
            <select
              id="location-name-dropdown"
              name="location-name"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white appearance-none"
            >
              <option value="">Select Location</option>
            </select>

            <!-- Input for new location name -->
            <input
              type="text"
              id="location-name-input"
              name="location-name"
              placeholder="Enter New Location Name"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white hidden"
            />

            <!-- Editable input for modifying existing location label -->
            <input
              type="text"
              id="location-label-edit"
              name="location-label-edit"
              placeholder="Edit Location Label"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white hidden"
            />
          </div>
        </div>

        <div class="flex space-x-4 items-end">
          <button
            type="button"
            id="viewButton"
            onclick="viewLocation()"
            class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out"
          >
            <i class="fas fa-eye mr-2"></i>View
          </button>
          <button
            type="button"
            id="editLabelButton"
            onclick="toggleLabelEdit()"
            class="bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out hidden"
          >
            <i class="fas fa-edit mr-2"></i>Edit Label
          </button>
        </div>

        <div class="flex items-end space-x-4">
          <input
            type="text"
            id="asana-link"
            name="asana-link"
            placeholder="Asana Link"
            class="flex-grow px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
          <button
            type="button"
            id="uploadButton"
            onclick="uploadFromAsana()"
            class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out"
          >
            <i class="fas fa-upload mr-2"></i>Upload
          </button>
        </div>
      </div>

      <!-- Row 2 -->
      <div class="grid grid-cols-1 gap-6">
        <div>
          <label
            for="shipping-address"
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
          >
            Shipping Address
            <span class="text-red-500">*</span>
          </label>
          <textarea
            id="shipping-address"
            name="shipping-address"
            rows="3"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          ></textarea>
        </div>
      </div>

      <!-- Row 3 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
          <label
            for="contact-person"
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
          >
            Contact Person
            <span class="text-red-500">*</span>
          </label>
          <input
            type="text"
            id="contact-person"
            name="contact-person"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
        </div>

        <div>
          <label
            for="phone"
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
          >
            Phone Number
            <span class="text-red-500">*</span>
          </label>
          <input
            type="tel"
            id="phone"
            name="phone"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
        </div>

        <div>
          <label
            for="email"
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
          >
            Email
            <span class="text-red-500">*</span>
          </label>
          <input
            type="email"
            id="email"
            name="email"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
        </div>
      </div>

      <!-- Hidden Fields -->
      <input type="hidden" id="updated-by" name="updated-by" value="system" />
      <input type="hidden" id="last-updated" name="last-updated" />
      <input
        type="hidden"
        id="original-location-id"
        name="original-location-id"
      />
      <input type="hidden" id="is-editing" name="is-editing" value="false" />

      <!-- Action Buttons -->
      <div class="flex flex-wrap gap-3">
        <button
          type="button"
          id="addButton"
          onclick="addLocation()"
          class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out"
          data-permission="add"
        >
          <i class="fas fa-plus mr-2"></i>Add
        </button>

        <button
          type="button"
          id="saveButton"
          onclick="saveLocation()"
          class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out hidden"
          data-permission="modify"
        >
          <i class="fas fa-save mr-2"></i>Save Changes
        </button>

        <button
          type="button"
          id="modifyButton"
          onclick="modifyLocation()"
          class="bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out"
          data-permission="modify"
        >
          <i class="fas fa-edit mr-2"></i>Modify
        </button>

        <button
          type="button"
          id="cancelEditButton"
          onclick="cancelEdit()"
          class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out hidden"
        >
          <i class="fas fa-times mr-2"></i>Cancel
        </button>

        <button
          type="button"
          id="deleteButton"
          onclick="deleteLocation()"
          class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out"
          data-permission="delete"
        >
          <i class="fas fa-trash-alt mr-2"></i>Delete
        </button>
      </div>
    </form>
  </div>

  <!-- Keyboard Shortcuts Guide -->
  <div
    class="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 text-sm text-gray-600 dark:text-gray-400"
  >
    <h3 class="font-medium mb-2">Keyboard Shortcuts</h3>
    <div class="grid grid-cols-2 gap-2">
      <div>⌘/Ctrl + S = Save</div>
      <div>⌘/Ctrl + N = New</div>
      <div>⌘/Ctrl + E = Export</div>
      <div>Esc = Clear form</div>
    </div>
  </div>
</div>
<!-- Toast Container -->
<div id="toast-container" class="fixed bottom-4 right-4 z-50"></div>
{% endblock %} {% block extra_js %}

<script src="{{ url_for('static', filename='js/location_management.js') }}"></script>
{% endblock %}
