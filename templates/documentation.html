{% extends "base.html" %} {% block title %}Documentation - Talaria Dashboard{%
endblock %} {% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Header Section -->
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
      Talaria Dashboard Documentation
    </h1>
    <p class="mt-2 text-lg text-gray-600 dark:text-gray-300">
      Complete guide to using the Talaria Dashboard for comprehensive wafer
      shipment management, inventory tracking, and automation
    </p>
  </div>

  <!-- Modern Search Bar -->
  <div class="mb-8">
    <div class="relative max-w-2xl mx-auto">
      <!-- Search Input Container -->
      <div class="relative group">
        <div
          class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none"
        >
          <svg
            class="h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors duration-200"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
        <input
          type="text"
          id="modern-search"
          class="w-full pl-12 pr-12 py-4 text-lg bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600 rounded-xl shadow-sm focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20 dark:focus:ring-blue-400/20 transition-all duration-200 placeholder-gray-500 dark:placeholder-gray-400 dark:text-white"
          placeholder="Search documentation... (e.g., 'label printing', 'inventory', 'shipping')"
          autocomplete="off"
          spellcheck="false"
        />
        <div class="absolute inset-y-0 right-0 pr-4 flex items-center">
          <div id="search-status" class="hidden">
            <div
              class="animate-spin rounded-full h-5 w-5 border-2 border-blue-500 border-t-transparent"
            ></div>
          </div>
          <button
            id="clear-search"
            class="hidden p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
          >
            <svg
              class="h-4 w-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      </div>

      <!-- Search Results Summary -->
      <div
        id="search-summary"
        class="hidden mt-3 px-4 py-2 bg-blue-50 dark:bg-blue-900/30 rounded-lg border border-blue-200 dark:border-blue-800"
      >
        <div class="flex items-center justify-between">
          <span
            id="results-text"
            class="text-sm text-blue-700 dark:text-blue-300"
          ></span>
          <button
            id="clear-results"
            class="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 font-medium"
          >
            Clear search
          </button>
        </div>
      </div>

      <!-- Quick Search Suggestions -->
      <div
        id="search-suggestions"
        class="mt-3 grid grid-cols-2 md:grid-cols-4 gap-2"
      >
        <button
          class="search-suggestion px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200"
          data-term="label printing"
        >
          🏷️ Label Printing
        </button>
        <button
          class="search-suggestion px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200"
          data-term="inventory"
        >
          📦 Inventory
        </button>
        <button
          class="search-suggestion px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200"
          data-term="shipping"
        >
          🚚 Shipping
        </button>
        <button
          class="search-suggestion px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200"
          data-term="asana"
        >
          🔗 Asana
        </button>
      </div>
    </div>
  </div>

  <!-- Documentation Content -->
  <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
    <!-- Sidebar Navigation -->
    <div class="md:col-span-1">
      <nav class="space-y-1 sticky top-20" id="doc-nav">
        <a
          href="#getting-started"
          class="block px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150 text-gray-600 hover:text-gray-900 hover:bg-gray-50 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-700"
        >
          <i class="fas fa-play-circle mr-2"></i>
          Getting Started
        </a>
        <a
          href="#navigation-overview"
          class="block px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150 text-gray-600 hover:text-gray-900 hover:bg-gray-50 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-700"
        >
          <i class="fas fa-compass mr-2"></i>
          Navigation Overview
        </a>
        <a
          href="#shipment-task"
          class="block px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150 text-gray-600 hover:text-gray-900 hover:bg-gray-50 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-700"
        >
          <i class="fas fa-truck mr-2"></i>
          Shipment Task
        </a>
        <a
          href="#inventory-management"
          class="block px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150 text-gray-600 hover:text-gray-900 hover:bg-gray-50 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-700"
        >
          <i class="fas fa-boxes mr-2"></i>
          Inventory Management
        </a>
        <a
          href="#label-printing"
          class="block px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150 text-gray-600 hover:text-gray-900 hover:bg-gray-50 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-700"
        >
          <i class="fas fa-print mr-2"></i>
          Label Printing
        </a>
        <a
          href="#asana-integration"
          class="block px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150 text-gray-600 hover:text-gray-900 hover:bg-gray-50 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-700"
        >
          <i class="fas fa-link mr-2"></i>
          Asana Integration
        </a>
        <a
          href="#shipping"
          class="block px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150 text-gray-600 hover:text-gray-900 hover:bg-gray-50 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-700"
        >
          <i class="fas fa-shipping-fast mr-2"></i>
          Shipping Process
        </a>
        <a
          href="#support-system"
          class="block px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150 text-gray-600 hover:text-gray-900 hover:bg-gray-50 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-700"
        >
          <i class="fas fa-headset mr-2"></i>
          Support System
        </a>
        <a
          href="#chat-assistant"
          class="block px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150 text-gray-600 hover:text-gray-900 hover:bg-gray-50 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-700"
        >
          <i class="fas fa-robot mr-2"></i>
          Chat Assistant
        </a>

        <a
          href="#notification-system"
          class="block px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150 text-gray-600 hover:text-gray-900 hover:bg-gray-50 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-700"
        >
          <i class="fas fa-bell mr-2"></i>
          Notification System
        </a>
        <a
          href="#advanced-features"
          class="block px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150 text-gray-600 hover:text-gray-900 hover:bg-gray-50 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-700"
        >
          <i class="fas fa-cogs mr-2"></i>
          Advanced Features
        </a>
        <a
          href="#faq"
          class="block px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150 text-gray-600 hover:text-gray-900 hover:bg-gray-50 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-700"
        >
          <i class="fas fa-question-circle mr-2"></i>
          Frequently Asked Questions
        </a>
        <a
          href="#troubleshooting"
          class="block px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150 text-gray-600 hover:text-gray-900 hover:bg-gray-50 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-700"
        >
          <i class="fas fa-wrench mr-2"></i>
          Troubleshooting
        </a>
      </nav>
    </div>

    <!-- Main Content -->
    <div class="md:col-span-3 space-y-8">
      <!-- Getting Started Section -->
      <section id="getting-started" class="scroll-mt-8">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Getting Started
        </h2>
        <div class="text-gray-800 dark:text-gray-200 max-w-none">
          <p class="mb-4">
            Welcome to Talaria Dashboard - a comprehensive enterprise-grade
            operations management platform designed specifically for
            semiconductor wafer tracking, inventory management, and shipping
            logistics.
          </p>
          <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg mb-6">
            <h4 class="font-semibold mb-2">What's New in This Version</h4>
            <ul class="space-y-2">
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                Enhanced support system with ticket management
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                AI-powered chat assistant with training interface
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                Substrate wafer label printing support
              </li>
              <li class="flex items-center">
                <i class="fas fa-clock text-orange-500 mr-2"></i>
                UPS shipping integration (planned for future release)
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                Advanced inventory management with real-time updates
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                RFQ automation email system with Excel integration
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                In-app bell notification system with real-time alerts
              </li>
            </ul>
          </div>
        </div>
      </section>

      <!-- Navigation Overview Section -->
      <section id="navigation-overview" class="scroll-mt-8">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Navigation Overview
        </h2>
        <div class="text-gray-800 dark:text-gray-200 max-w-none">
          <p class="mb-6">
            Talaria Dashboard is organized into logical sections to help you
            efficiently manage your workflow. Here's an overview of the main
            navigation areas:
          </p>

          <!-- Main Menu -->
          <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm mb-6">
            <h3 class="text-xl font-semibold mb-4">Main Menu</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div
                class="flex items-start p-3 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <i class="fas fa-home text-blue-500 mt-1 mr-3"></i>
                <div>
                  <h4 class="font-semibold">Dashboard</h4>
                  <p class="text-sm">
                    Real-time overview of inventory, shipments, and key metrics
                  </p>
                </div>
              </div>
              <div
                class="flex items-start p-3 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <i class="fas fa-robot text-blue-500 mt-1 mr-3"></i>
                <div>
                  <h4 class="font-semibold">ChatBot</h4>
                  <p class="text-sm">
                    AI-powered assistant for quick help and guidance
                  </p>
                </div>
              </div>
              <div
                class="flex items-start p-3 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <i class="fas fa-chart-bar text-blue-500 mt-1 mr-3"></i>
                <div>
                  <h4 class="font-semibold">Statistics</h4>
                  <p class="text-sm">
                    Comprehensive analytics and reporting dashboard
                  </p>
                </div>
              </div>
              <div
                class="flex items-start p-3 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <i class="fas fa-info-circle text-blue-500 mt-1 mr-3"></i>
                <div>
                  <h4 class="font-semibold">About</h4>
                  <p class="text-sm">System information and version details</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Tools & Features -->
          <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm mb-6">
            <h3 class="text-xl font-semibold mb-4">Tools & Features</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div
                class="flex items-start p-3 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <i class="fas fa-boxes text-green-500 mt-1 mr-3"></i>
                <div>
                  <h4 class="font-semibold">Inventory</h4>
                  <p class="text-sm">
                    Comprehensive wafer inventory management and tracking
                  </p>
                </div>
              </div>
              <div
                class="flex items-start p-3 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <i class="fas fa-truck text-green-500 mt-1 mr-3"></i>
                <div>
                  <h4 class="font-semibold">Shipment Task</h4>
                  <p class="text-sm">
                    Manage shipments and track delivery status
                  </p>
                </div>
              </div>
              <div
                class="flex items-start p-3 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <i class="fas fa-box-open text-green-500 mt-1 mr-3"></i>
                <div>
                  <h4 class="font-semibold">Print Label with Asana</h4>
                  <p class="text-sm">
                    Generate labels with Asana task integration
                  </p>
                </div>
              </div>
              <div
                class="flex items-start p-3 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <i class="fas fa-shipping-fast text-green-500 mt-1 mr-3"></i>
                <div>
                  <h4 class="font-semibold">UPS Shipping</h4>
                  <p class="text-sm">
                    UPS is our primary carrier. Full API integration planned for
                    future release
                  </p>
                </div>
              </div>
              <div
                class="flex items-start p-3 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <i class="fas fa-map-marker-alt text-green-500 mt-1 mr-3"></i>
                <div>
                  <h4 class="font-semibold">Location Management</h4>
                  <p class="text-sm">Organize and track storage locations</p>
                </div>
              </div>
              <div
                class="flex items-start p-3 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <i class="fas fa-tags text-green-500 mt-1 mr-3"></i>
                <div>
                  <h4 class="font-semibold">Label Templates</h4>
                  <p class="text-sm">Manage and customize label templates</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Support & Help -->
          <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
            <h3 class="text-xl font-semibold mb-4">Support & Help</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div
                class="flex items-start p-3 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <i class="fas fa-book text-purple-500 mt-1 mr-3"></i>
                <div>
                  <h4 class="font-semibold">Documentation</h4>
                  <p class="text-sm">Complete user guide and tutorials</p>
                </div>
              </div>
              <div
                class="flex items-start p-3 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <i class="fas fa-question-circle text-purple-500 mt-1 mr-3"></i>
                <div>
                  <h4 class="font-semibold">FAQs</h4>
                  <p class="text-sm">Frequently asked questions</p>
                </div>
              </div>
              <div
                class="flex items-start p-3 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <i class="fas fa-headset text-purple-500 mt-1 mr-3"></i>
                <div>
                  <h4 class="font-semibold">Support</h4>
                  <p class="text-sm">
                    Submit tickets and get technical assistance
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Shipment Task Management Section -->
      <section id="shipment-task" class="scroll-mt-8">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Shipment Task Management
        </h2>
        <div class="text-gray-800 dark:text-gray-200 max-w-none">
          <p class="mb-6">
            The Shipment Task feature provides a comprehensive workflow for
            managing wafer shipments from creation to delivery. This system
            integrates with Asana for task management and includes advanced
            wafer selection capabilities.
          </p>

          <!-- Key Features -->
          <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg mb-6">
            <h4 class="font-semibold mb-2">Shipment Task Features</h4>
            <ul class="space-y-2">
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                Asana integration for automatic task data population
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                Advanced wafer inventory selection with real-time filtering
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                Support for Erfurt and Eiger specific requirements
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                Integrated carrier information and shipping details
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                Batch wafer selection and management
              </li>
            </ul>
          </div>

          <!-- Workflow Steps -->
          <h3 class="text-xl font-semibold mt-6 mb-4">
            Shipment Creation Workflow
          </h3>
          <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm mb-6">
            <div class="space-y-6">
              <div
                class="flex items-start p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <div
                  class="flex-shrink-0 w-8 h-8 flex items-center justify-center bg-blue-100 dark:bg-blue-900 rounded-full mr-4"
                >
                  <span class="font-bold text-blue-500">1</span>
                </div>
                <div>
                  <h4 class="font-semibold mb-1">Asana Task Integration</h4>
                  <p class="text-sm mb-2">
                    Start by pasting an Asana task URL to automatically populate
                    shipment details
                  </p>
                  <div
                    class="mt-2 p-2 bg-gray-50 dark:bg-gray-700 rounded text-sm"
                  >
                    <code>https://app.asana.com/0/[project-id]/[task-id]</code>
                  </div>
                </div>
              </div>

              <div
                class="flex items-start p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <div
                  class="flex-shrink-0 w-8 h-8 flex items-center justify-center bg-blue-100 dark:bg-blue-900 rounded-full mr-4"
                >
                  <span class="font-bold text-blue-500">2</span>
                </div>
                <div>
                  <h4 class="font-semibold mb-1">
                    Shipment Task Configuration
                  </h4>
                  <p class="text-sm">Configure shipment details including:</p>
                  <ul class="mt-2 space-y-1 text-sm list-disc pl-4">
                    <li>
                      Type (Standard/Critical) and Priority (Low/Medium/High)
                    </li>
                    <li>Title and Shipment Date</li>
                    <li>Wafer selection method (Random/Not Random)</li>
                    <li>
                      Special options (Need Reviewing, Label Free, Keep Cassette
                      Closed)
                    </li>
                  </ul>
                </div>
              </div>

              <div
                class="flex items-start p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <div
                  class="flex-shrink-0 w-8 h-8 flex items-center justify-center bg-blue-100 dark:bg-blue-900 rounded-full mr-4"
                >
                  <span class="font-bold text-blue-500">3</span>
                </div>
                <div>
                  <h4 class="font-semibold mb-1">
                    Customer-Specific Information
                  </h4>
                  <p class="text-sm">
                    Add specialized fields based on destination:
                  </p>
                  <div class="mt-2 grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h5 class="font-medium text-sm">For Erfurt:</h5>
                      <ul class="text-xs list-disc pl-4">
                        <li>XFAB PO and Device ID</li>
                        <li>Project ID</li>
                      </ul>
                    </div>
                    <div>
                      <h5 class="font-medium text-sm">For Eiger:</h5>
                      <ul class="text-xs list-disc pl-4">
                        <li>Eiger Number, Tapeout, Vendor/Customer Lot</li>
                        <li>
                          Technical specs (Rib, TOX Target, Heater, Undercut)
                        </li>
                        <li>SiN Tube Position, Mask type</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <div
                class="flex items-start p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <div
                  class="flex-shrink-0 w-8 h-8 flex items-center justify-center bg-blue-100 dark:bg-blue-900 rounded-full mr-4"
                >
                  <span class="font-bold text-blue-500">4</span>
                </div>
                <div>
                  <h4 class="font-semibold mb-1">Wafer Selection</h4>
                  <p class="text-sm">Use the integrated inventory table to:</p>
                  <ul class="mt-2 space-y-1 text-sm list-disc pl-4">
                    <li>Search and filter available wafers</li>
                    <li>Select individual wafers or use batch selection</li>
                    <li>
                      View real-time wafer details (Lot ID, Location, Dates)
                    </li>
                    <li>Track selection count and validate availability</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- Advanced Features -->
          <h3 class="text-xl font-semibold mt-6 mb-4">
            Advanced Inventory Integration
          </h3>
          <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm mb-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 class="font-semibold mb-3">Real-time Wafer Table</h4>
                <ul class="space-y-2 text-sm">
                  <li class="flex items-start">
                    <i class="fas fa-table text-blue-500 mt-1 mr-2"></i>
                    <span>Sortable columns for all wafer attributes</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fas fa-search text-blue-500 mt-1 mr-2"></i>
                    <span>Advanced search and filtering capabilities</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fas fa-check-square text-blue-500 mt-1 mr-2"></i>
                    <span>Batch selection with select-all functionality</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fas fa-history text-blue-500 mt-1 mr-2"></i>
                    <span>Wafer history and location tracking</span>
                  </li>
                </ul>
              </div>
              <div>
                <h4 class="font-semibold mb-3">Smart Selection Features</h4>
                <ul class="space-y-2 text-sm">
                  <li class="flex items-start">
                    <i class="fas fa-sync text-green-500 mt-1 mr-2"></i>
                    <span>Real-time inventory synchronization</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fas fa-filter text-green-500 mt-1 mr-2"></i>
                    <span>Filter by lot ID, location, dates</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fas fa-list-ol text-green-500 mt-1 mr-2"></i>
                    <span>Pagination for large inventories</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fas fa-download text-green-500 mt-1 mr-2"></i>
                    <span>Export selected wafers data</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Carrier Information -->
          <h3 class="text-xl font-semibold mt-6 mb-4">
            Carrier & Shipping Details
          </h3>
          <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <h4 class="font-semibold mb-2">Contact Information</h4>
                <ul class="text-sm space-y-1">
                  <li>Contact Person & Email</li>
                  <li>Telephone Number</li>
                  <li>Destination Address</li>
                </ul>
              </div>
              <div>
                <h4 class="font-semibold mb-2">Shipping Options</h4>
                <ul class="text-sm space-y-1">
                  <li>Location Selection</li>
                  <li>Parcel Size Configuration</li>
                  <li>Destination Label</li>
                </ul>
              </div>
              <div>
                <h4 class="font-semibold mb-2">Label Information</h4>
                <ul class="text-sm space-y-1">
                  <li>Custom Label Title</li>
                  <li>Wafer Count Validation</li>
                  <li>Additional Comments</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Inventory Management Section -->
      <section id="inventory-management" class="scroll-mt-8">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Inventory Management & Wafer Tracking
        </h2>
        <div class="text-gray-800 dark:text-gray-200 max-w-none">
          <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg mb-6">
            <h4 class="font-semibold mb-2">Enhanced Inventory Features</h4>
            <ul class="space-y-2">
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                Real-time inventory tracking with automatic synchronization
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                Advanced search and filtering capabilities
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                Metadata management with custom fields
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                Batch operations for multiple wafers
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                Export capabilities in multiple formats
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                Automated location updates and tracking
              </li>
            </ul>
          </div>

          <h3 class="text-xl font-semibold mt-6 mb-4">Lot Selection Methods</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
              <h4 class="font-semibold mb-2">Single Lot Selection</h4>
              <ol class="list-decimal pl-5 space-y-2 text-sm">
                <li>Navigate to the main dashboard</li>
                <li>Use the lot dropdown selector</li>
                <li>Choose your desired lot</li>
                <li>Click "Continue" to proceed</li>
              </ol>
            </div>
            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
              <h4 class="font-semibold mb-2">Multiple Lots Selection</h4>
              <ol class="list-decimal pl-5 space-y-2 text-sm">
                <li>Enter multiple lot numbers</li>
                <li>Separate lots with commas</li>
                <li>System validates all entries</li>
                <li>Click "Continue" when ready</li>
              </ol>
            </div>
          </div>

          <h3 class="text-xl font-semibold mt-6 mb-4">Wafer Tracking System</h3>
          <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg mb-6">
            <div class="flex flex-col space-y-4">
              <div class="flex items-start">
                <i class="fas fa-search text-blue-500 mt-1 mr-3"></i>
                <div>
                  <h4 class="font-semibold">Advanced Search</h4>
                  <p class="text-sm">
                    Use filters for lot number, scribe ID, location, and date
                    ranges
                  </p>
                </div>
              </div>
              <div class="flex items-start">
                <i class="fas fa-history text-blue-500 mt-1 mr-3"></i>
                <div>
                  <h4 class="font-semibold">Location History</h4>
                  <p class="text-sm">
                    Track wafer movement and shipping status
                  </p>
                </div>
              </div>
              <div class="flex items-start">
                <i class="fas fa-sync text-blue-500 mt-1 mr-3"></i>
                <div>
                  <h4 class="font-semibold">Real-time Updates</h4>
                  <p class="text-sm">
                    Automatic synchronization with inventory database
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Label Printing System Section -->
      <section id="label-printing" class="scroll-mt-8">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Label Printing System
        </h2>
        <div class="text-gray-800 dark:text-gray-200 max-w-none">
          <h3 class="text-xl font-semibold mb-4">Label Types</h3>
          <div
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6"
          >
            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
              <div class="text-center mb-3">
                <i class="fas fa-tag text-2xl text-blue-500"></i>
              </div>
              <h4 class="font-semibold text-center mb-2">Standard Label</h4>
              <p class="text-sm text-center">
                Default label type with company logo
              </p>
            </div>
            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
              <div class="text-center mb-3">
                <i class="fas fa-industry text-2xl text-green-500"></i>
              </div>
              <h4 class="font-semibold text-center mb-2">Erfurt Label</h4>
              <p class="text-sm text-center">
                Specialized format for Erfurt shipments
              </p>
            </div>
            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
              <div class="text-center mb-3">
                <i class="fas fa-square text-2xl text-purple-500"></i>
              </div>
              <h4 class="font-semibold text-center mb-2">Label-free</h4>
              <p class="text-sm text-center">No logo, basic information only</p>
            </div>
            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
              <div class="text-center mb-3">
                <i class="fas fa-microchip text-2xl text-orange-500"></i>
              </div>
              <h4 class="font-semibold text-center mb-2">Substrate Wafer</h4>
              <p class="text-sm text-center">
                Special format for substrate wafers (no packing slip required)
              </p>
            </div>
          </div>

          <div class="bg-yellow-50 dark:bg-yellow-900 p-4 rounded-lg mb-6">
            <h4 class="font-semibold mb-2 flex items-center">
              <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
              Label Information Guide
            </h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h5 class="font-medium mb-2">Required Fields:</h5>
                <ul class="list-disc pl-5 space-y-1 text-sm">
                  <li>Shipment Date</li>
                  <li>Label Title</li>
                  <li>Wafer Count</li>
                  <li>Wafer IDs</li>
                </ul>
              </div>
              <div>
                <h5 class="font-medium mb-2">Optional Fields:</h5>
                <ul class="list-disc pl-5 space-y-1 text-sm">
                  <li>Purchase Order (Erfurt)</li>
                  <li>Project ID (Erfurt)</li>
                  <li>X-FAB Lot ID (Erfurt)</li>
                  <li>Comments</li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Printing Process -->
          <h3 class="text-xl font-semibold mt-6 mb-4">Printing Process</h3>
          <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm mb-6">
            <div class="flex flex-col space-y-6">
              <div class="flex items-center justify-between relative">
                <div class="w-64 p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
                  <i class="fas fa-edit text-blue-500 mb-2"></i>
                  <h4 class="font-semibold">1. Enter Details</h4>
                  <p class="text-sm">Fill in required information</p>
                </div>
                <div
                  class="hidden md:block absolute left-1/2 top-1/2 transform -translate-y-1/2"
                >
                  <i class="fas fa-arrow-right text-gray-400"></i>
                </div>
              </div>
              <div class="flex items-center justify-between relative">
                <div class="w-64 p-4 bg-green-50 dark:bg-green-900 rounded-lg">
                  <i class="fas fa-check-circle text-green-500 mb-2"></i>
                  <h4 class="font-semibold">2. Verify Information</h4>
                  <p class="text-sm">Review all entered data</p>
                </div>
                <div
                  class="hidden md:block absolute left-1/2 top-1/2 transform -translate-y-1/2"
                >
                  <i class="fas fa-arrow-right text-gray-400"></i>
                </div>
              </div>
              <div class="flex items-center justify-between">
                <div
                  class="w-64 p-4 bg-purple-50 dark:bg-purple-900 rounded-lg"
                >
                  <i class="fas fa-print text-purple-500 mb-2"></i>
                  <h4 class="font-semibold">3. Print Label</h4>
                  <p class="text-sm">Send to connected printer</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Printer Setup -->
          <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h4 class="font-semibold mb-3">Printer Configuration</h4>
            <div class="space-y-3">
              <div class="flex items-start">
                <i class="fas fa-wifi text-blue-500 mt-1 mr-3"></i>
                <div>
                  <p class="font-medium">Network Connection</p>
                  <p class="text-sm">Ensure printer is connected to network</p>
                </div>
              </div>
              <div class="flex items-start">
                <i class="fas fa-cog text-blue-500 mt-1 mr-3"></i>
                <div>
                  <p class="font-medium">IP Configuration</p>
                  <p class="text-sm">Enter printer IP in settings</p>
                </div>
              </div>
              <div class="flex items-start">
                <i class="fas fa-print text-blue-500 mt-1 mr-3"></i>
                <div>
                  <p class="font-medium">Test Print</p>
                  <p class="text-sm">Verify connection with test print</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Asana Integration Section -->
      <section id="asana-integration" class="scroll-mt-8">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Asana Integration
        </h2>
        <div class="text-gray-800 dark:text-gray-200 max-w-none">
          <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg mb-6">
            <h4 class="font-semibold mb-2">Integration Features</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <ul class="space-y-2">
                <li class="flex items-center">
                  <i class="fas fa-sync text-blue-500 mr-2"></i>
                  Automatic task synchronization
                </li>
                <li class="flex items-center">
                  <i class="fas fa-link text-blue-500 mr-2"></i>
                  Direct task linking
                </li>
                <li class="flex items-center">
                  <i class="fas fa-bell text-blue-500 mr-2"></i>
                  Automated notifications
                </li>
              </ul>
              <ul class="space-y-2">
                <li class="flex items-center">
                  <i class="fas fa-tasks text-blue-500 mr-2"></i>
                  Task status tracking
                </li>
                <li class="flex items-center">
                  <i class="fas fa-clipboard-check text-blue-500 mr-2"></i>
                  Shipment verification
                </li>
                <li class="flex items-center">
                  <i class="fas fa-history text-blue-500 mr-2"></i>
                  Activity logging
                </li>
              </ul>
            </div>
          </div>

          <h3 class="text-xl font-semibold mt-6 mb-4">Task Management Flow</h3>
          <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm mb-6">
            <div class="space-y-6">
              <div
                class="flex items-start p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <div
                  class="flex-shrink-0 w-8 h-8 flex items-center justify-center bg-blue-100 dark:bg-blue-900 rounded-full mr-4"
                >
                  <span class="font-bold text-blue-500">1</span>
                </div>
                <div>
                  <h4 class="font-semibold mb-1">Task Creation & Linking</h4>
                  <p class="text-sm">
                    Enter Asana task URL to link with shipment
                  </p>
                  <div
                    class="mt-2 p-2 bg-gray-50 dark:bg-gray-700 rounded text-sm"
                  >
                    <code>https://app.asana.com/0/[project-id]/[task-id]</code>
                  </div>
                </div>
              </div>

              <div
                class="flex items-start p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <div
                  class="flex-shrink-0 w-8 h-8 flex items-center justify-center bg-blue-100 dark:bg-blue-900 rounded-full mr-4"
                >
                  <span class="font-bold text-blue-500">2</span>
                </div>
                <div>
                  <h4 class="font-semibold mb-1">Data Synchronization</h4>
                  <p class="text-sm">System automatically pulls:</p>
                  <ul class="mt-2 space-y-1 text-sm list-disc pl-4">
                    <li>Label title from task name</li>
                    <li>Wafer information</li>
                    <li>Shipping requirements</li>
                    <li>Customer details</li>
                  </ul>
                </div>
              </div>

              <div
                class="flex items-start p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <div
                  class="flex-shrink-0 w-8 h-8 flex items-center justify-center bg-blue-100 dark:bg-blue-900 rounded-full mr-4"
                >
                  <span class="font-bold text-blue-500">3</span>
                </div>
                <div>
                  <h4 class="font-semibold mb-1">Status Updates</h4>
                  <p class="text-sm">Automatic task updates for:</p>
                  <ul class="mt-2 space-y-1 text-sm list-disc pl-4">
                    <li>Label generation</li>
                    <li>Shipping initiation</li>
                    <li>Delivery confirmation</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- Important Fields -->
          <h3 class="text-xl font-semibold mt-6 mb-4">
            Important Asana Fields
          </h3>
          <div class="overflow-x-auto">
            <table class="min-w-full bg-white dark:bg-gray-800 rounded-lg">
              <thead>
                <tr class="bg-gray-50 dark:bg-gray-700">
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                  >
                    Field Name
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                  >
                    Purpose
                  </th>
                  <th
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                  >
                    Required
                  </th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap text-sm">
                    LGT Lot ID
                  </td>
                  <td class="px-6 py-4 text-sm">Identifies the lot number</td>
                  <td class="px-6 py-4 text-sm">
                    <i class="fas fa-check text-green-500"></i>
                  </td>
                </tr>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap text-sm">
                    Wafers IDs
                  </td>
                  <td class="px-6 py-4 text-sm">Lists selected wafers</td>
                  <td class="px-6 py-4 text-sm">
                    <i class="fas fa-check text-green-500"></i>
                  </td>
                </tr>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap text-sm">
                    Label Title
                  </td>
                  <td class="px-6 py-4 text-sm">Sets shipping label title</td>
                  <td class="px-6 py-4 text-sm">
                    <i class="fas fa-check text-green-500"></i>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>

      <!-- Shipping Process Section -->
      <section id="shipping" class="scroll-mt-8">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Shipping Process
        </h2>
        <div class="text-gray-800 dark:text-gray-200 max-w-none">
          <p class="mb-6">
            Talaria Dashboard manages the complete shipping workflow from
            package preparation to delivery confirmation.
            <strong>UPS is our primary carrier</strong>, and we're planning full
            API integration in future releases to streamline label generation
            and tracking directly within the platform.
          </p>

          <!-- Process Timeline -->
          <div
            class="relative bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm mb-6"
          >
            <div class="space-y-8">
              <div class="flex">
                <div
                  class="flex-shrink-0 w-12 h-12 flex items-center justify-center bg-blue-100 dark:bg-blue-900 rounded-full"
                >
                  <i class="fas fa-box text-blue-500"></i>
                </div>
                <div class="ml-4">
                  <h4 class="font-semibold">1. Package Preparation</h4>
                  <p class="text-sm mt-1">
                    - Verify wafer selection<br />
                    - Generate shipping labels<br />
                    - Print packing slip
                  </p>
                </div>
              </div>
              <div class="flex">
                <div
                  class="flex-shrink-0 w-12 h-12 flex items-center justify-center bg-green-100 dark:bg-green-900 rounded-full"
                >
                  <i class="fas fa-truck text-green-500"></i>
                </div>
                <div class="ml-4">
                  <h4 class="font-semibold">2. Shipping Initiation</h4>
                  <p class="text-sm mt-1">
                    - Update inventory status<br />
                    - Enter tracking number<br />
                    - Send shipping notification
                  </p>
                </div>
              </div>
              <div class="flex">
                <div
                  class="flex-shrink-0 w-12 h-12 flex items-center justify-center bg-purple-100 dark:bg-purple-900 rounded-full"
                >
                  <i class="fas fa-check-circle text-purple-500"></i>
                </div>
                <div class="ml-4">
                  <h4 class="font-semibold">3. Delivery Confirmation</h4>
                  <p class="text-sm mt-1">
                    - Track delivery status<br />
                    - Confirm receipt<br />
                    - Update Asana task
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- Notification System -->
          <h3 class="text-xl font-semibold mt-6 mb-4">Notification System</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
              <h4 class="font-semibold mb-3">Automatic Notifications</h4>
              <ul class="space-y-2 text-sm">
                <li class="flex items-start">
                  <i class="fas fa-paper-plane text-blue-500 mt-1 mr-2"></i>
                  <span>Shipping confirmation to customer</span>
                </li>
                <li class="flex items-start">
                  <i class="fas fa-bell text-blue-500 mt-1 mr-2"></i>
                  <span>Internal team updates</span>
                </li>
                <li class="flex items-start">
                  <i class="fas fa-envelope text-blue-500 mt-1 mr-2"></i>
                  <span>Delivery confirmation alerts</span>
                </li>
              </ul>
            </div>
            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
              <h4 class="font-semibold mb-3">Tracking Updates</h4>
              <ul class="space-y-2 text-sm">
                <li class="flex items-start">
                  <i class="fas fa-location-arrow text-green-500 mt-1 mr-2"></i>
                  <span>Real-time location tracking</span>
                </li>
                <li class="flex items-start">
                  <i class="fas fa-clock text-green-500 mt-1 mr-2"></i>
                  <span>Estimated delivery times</span>
                </li>
                <li class="flex items-start">
                  <i
                    class="fas fa-exclamation-triangle text-green-500 mt-1 mr-2"
                  ></i>
                  <span>Delay notifications</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      <!-- Support System Section -->
      <section id="support-system" class="scroll-mt-8">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Support System
        </h2>
        <div class="text-gray-800 dark:text-gray-200 max-w-none">
          <p class="mb-6">
            Talaria Dashboard includes a comprehensive support system for
            technical assistance, feature requests, and issue reporting. The
            system provides multiple channels for getting help and ensures rapid
            response to critical issues.
          </p>

          <!-- Support Features -->
          <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg mb-6">
            <h4 class="font-semibold mb-2">Support Features</h4>
            <ul class="space-y-2">
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                Integrated ticket submission system
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                Automatic email notifications to support team
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                Priority-based ticket classification
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                Real-time system status monitoring
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                Live chat support integration
              </li>
            </ul>
          </div>

          <!-- Support Workflow -->
          <h3 class="text-xl font-semibold mt-6 mb-4">
            Support Ticket Process
          </h3>
          <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm mb-6">
            <div class="space-y-6">
              <div
                class="flex items-start p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <div
                  class="flex-shrink-0 w-8 h-8 flex items-center justify-center bg-blue-100 dark:bg-blue-900 rounded-full mr-4"
                >
                  <span class="font-bold text-blue-500">1</span>
                </div>
                <div>
                  <h4 class="font-semibold mb-1">Issue Identification</h4>
                  <p class="text-sm">Select the appropriate issue type:</p>
                  <ul class="mt-2 space-y-1 text-sm list-disc pl-4">
                    <li>
                      <strong>Technical:</strong> System errors, bugs,
                      performance issues
                    </li>
                    <li>
                      <strong>Account:</strong> User access, permissions,
                      authentication
                    </li>
                    <li>
                      <strong>Feature:</strong> Enhancement requests, new
                      functionality
                    </li>
                    <li>
                      <strong>Other:</strong> General questions and
                      miscellaneous issues
                    </li>
                  </ul>
                </div>
              </div>

              <div
                class="flex items-start p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <div
                  class="flex-shrink-0 w-8 h-8 flex items-center justify-center bg-blue-100 dark:bg-blue-900 rounded-full mr-4"
                >
                  <span class="font-bold text-blue-500">2</span>
                </div>
                <div>
                  <h4 class="font-semibold mb-1">Priority Assignment</h4>
                  <p class="text-sm">Choose the appropriate priority level:</p>
                  <div class="mt-2 grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="p-2 bg-red-50 dark:bg-red-900 rounded">
                      <h5 class="font-medium text-red-700 dark:text-red-300">
                        High Priority
                      </h5>
                      <p class="text-xs">
                        Critical issues affecting operations
                      </p>
                    </div>
                    <div class="p-2 bg-yellow-50 dark:bg-yellow-900 rounded">
                      <h5
                        class="font-medium text-yellow-700 dark:text-yellow-300"
                      >
                        Medium Priority
                      </h5>
                      <p class="text-xs">Important but not critical issues</p>
                    </div>
                    <div class="p-2 bg-green-50 dark:bg-green-900 rounded">
                      <h5
                        class="font-medium text-green-700 dark:text-green-300"
                      >
                        Low Priority
                      </h5>
                      <p class="text-xs">General questions and minor issues</p>
                    </div>
                  </div>
                </div>
              </div>

              <div
                class="flex items-start p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <div
                  class="flex-shrink-0 w-8 h-8 flex items-center justify-center bg-blue-100 dark:bg-blue-900 rounded-full mr-4"
                >
                  <span class="font-bold text-blue-500">3</span>
                </div>
                <div>
                  <h4 class="font-semibold mb-1">Ticket Submission</h4>
                  <p class="text-sm">Complete the support form with:</p>
                  <ul class="mt-2 space-y-1 text-sm list-disc pl-4">
                    <li>Clear subject line describing the issue</li>
                    <li>Detailed description of the problem</li>
                    <li>Steps to reproduce (if applicable)</li>
                    <li>Screenshots or attachments (optional)</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- Support Channels -->
          <h3 class="text-xl font-semibold mt-6 mb-4">Support Channels</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
              <h4 class="font-semibold mb-3">Direct Support</h4>
              <ul class="space-y-2 text-sm">
                <li class="flex items-start">
                  <i class="fas fa-envelope text-blue-500 mt-1 mr-2"></i>
                  <span>Email: <EMAIL></span>
                </li>
                <li class="flex items-start">
                  <i class="fas fa-phone text-blue-500 mt-1 mr-2"></i>
                  <span>Phone: +33 7 76 02 67 88</span>
                </li>
                <li class="flex items-start">
                  <i class="fas fa-ticket-alt text-blue-500 mt-1 mr-2"></i>
                  <span
                    >Support tickets automatically sent to support team</span
                  >
                </li>
              </ul>
            </div>
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
              <h4 class="font-semibold mb-3">Self-Service Options</h4>
              <ul class="space-y-2 text-sm">
                <li class="flex items-start">
                  <i class="fas fa-book text-green-500 mt-1 mr-2"></i>
                  <span>Comprehensive documentation</span>
                </li>
                <li class="flex items-start">
                  <i
                    class="fas fa-question-circle text-green-500 mt-1 mr-2"
                  ></i>
                  <span>Frequently Asked Questions (FAQs)</span>
                </li>
                <li class="flex items-start">
                  <i class="fas fa-robot text-green-500 mt-1 mr-2"></i>
                  <span>AI-powered chat assistant</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      <!-- Chat Assistant Section -->
      <section id="chat-assistant" class="scroll-mt-8">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Chat Assistant
        </h2>
        <div class="text-gray-800 dark:text-gray-200 max-w-none">
          <p class="mb-6">
            The Talaria Chat Assistant is an AI-powered help system that
            provides instant answers to common questions, guidance on using
            features, and interactive support. The system includes a training
            interface for administrators to continuously improve responses.
          </p>

          <!-- Chat Features -->
          <div class="bg-green-50 dark:bg-green-900 p-4 rounded-lg mb-6">
            <h4 class="font-semibold mb-2">Chat Assistant Capabilities</h4>
            <ul class="space-y-2">
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                Instant responses to common questions
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                Context-aware help based on current page
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                Dynamic time-based greetings and responses
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                Conversation logging and analytics
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                Admin training interface for response improvement
              </li>
            </ul>
          </div>

          <!-- Using the Chat Assistant -->
          <h3 class="text-xl font-semibold mt-6 mb-4">
            Using the Chat Assistant
          </h3>
          <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm mb-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 class="font-semibold mb-3">Getting Started</h4>
                <ol class="space-y-2 text-sm list-decimal pl-4">
                  <li>Click the "ChatBot" link in the main navigation</li>
                  <li>Type your question in the chat input field</li>
                  <li>Press Enter or click the send button</li>
                  <li>Receive instant responses from the AI assistant</li>
                </ol>
              </div>
              <div>
                <h4 class="font-semibold mb-3">Quick Suggestions</h4>
                <p class="text-sm mb-2">
                  Use these suggestion chips for common queries:
                </p>
                <div class="flex flex-wrap gap-2">
                  <span
                    class="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded text-xs"
                    >Help</span
                  >
                  <span
                    class="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded text-xs"
                    >What can you do?</span
                  >
                  <span
                    class="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded text-xs"
                    >Inventory</span
                  >
                  <span
                    class="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded text-xs"
                    >Shipments</span
                  >
                </div>
              </div>
            </div>
          </div>

          <!-- Admin Training Interface -->
          <h3 class="text-xl font-semibold mt-6 mb-4">
            Chat Training (Admin Only)
          </h3>
          <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <p class="text-sm mb-4">
              <strong>Note:</strong> The chat training interface is restricted
              to administrators only and allows for continuous improvement of
              the chat assistant's responses.
            </p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 class="font-semibold mb-2">Training Features</h4>
                <ul class="text-sm space-y-1">
                  <li>• Add new question patterns and responses</li>
                  <li>• Categorize responses by topic</li>
                  <li>• Set priority levels for responses</li>
                  <li>• Edit and update existing training data</li>
                </ul>
              </div>
              <div>
                <h4 class="font-semibold mb-2">Access Requirements</h4>
                <ul class="text-sm space-y-1">
                  <li>• Administrator role required</li>
                  <li>• Access via /chat/training URL</li>
                  <li>• Secure authentication verification</li>
                  <li>• Activity logging for audit trail</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Notification System Section -->
      <section id="notification-system" class="scroll-mt-8">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          In-App Notification System
        </h2>
        <div class="text-gray-800 dark:text-gray-200 max-w-none">
          <p class="mb-6">
            The In-App Notification System provides real-time alerts and
            communication within Talaria. This feature includes bell
            notifications, email integration, and comprehensive user alert
            management for enhanced workflow awareness and system communication.
          </p>

          <!-- Notification Features -->
          <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg mb-6">
            <h4 class="font-semibold mb-2">Notification System Features</h4>
            <ul class="space-y-2">
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                Real-time bell notification icon with unread count badge
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                Email integration for external notification delivery
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                Comprehensive notification management and tracking
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                User-specific notification filtering and organization
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                Automatic notification creation for system events
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-2"></i>
                Mark as read/unread functionality with status tracking
              </li>
            </ul>
          </div>

          <!-- Notification Types -->
          <h3 class="text-xl font-semibold mt-6 mb-4">Notification Types</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
              <div class="flex items-center mb-3">
                <i class="fas fa-envelope text-purple-500 text-xl mr-3"></i>
                <h4 class="font-semibold">Email Notifications</h4>
              </div>
              <ul class="space-y-1 text-sm">
                <li>• RFQ email confirmations</li>
                <li>• Support ticket submissions</li>
                <li>• System email alerts</li>
                <li>• Automated workflow updates</li>
              </ul>
            </div>
            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
              <div class="flex items-center mb-3">
                <i class="fas fa-cog text-blue-500 text-xl mr-3"></i>
                <h4 class="font-semibold">System Notifications</h4>
              </div>
              <ul class="space-y-1 text-sm">
                <li>• Application status updates</li>
                <li>• Database operation results</li>
                <li>• Error and warning alerts</li>
                <li>• Performance monitoring</li>
              </ul>
            </div>
            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
              <div class="flex items-center mb-3">
                <i class="fas fa-user text-green-500 text-xl mr-3"></i>
                <h4 class="font-semibold">User Notifications</h4>
              </div>
              <ul class="space-y-1 text-sm">
                <li>• Personal task reminders</li>
                <li>• Workflow completion alerts</li>
                <li>• Custom user messages</li>
                <li>• Activity confirmations</li>
              </ul>
            </div>
          </div>

          <!-- Bell Notification Interface -->
          <h3 class="text-xl font-semibold mt-6 mb-4">
            Bell Notification Interface
          </h3>
          <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm mb-6">
            <div class="space-y-4">
              <div
                class="flex items-start p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <div
                  class="flex-shrink-0 w-8 h-8 flex items-center justify-center bg-blue-100 dark:bg-blue-900 rounded-full mr-4"
                >
                  <i class="fas fa-bell text-blue-500"></i>
                </div>
                <div>
                  <h4 class="font-semibold mb-1">Bell Icon Display</h4>
                  <p class="text-sm">Located in the top navigation bar with:</p>
                  <ul class="mt-2 space-y-1 text-sm list-disc pl-4">
                    <li>Red badge showing unread notification count</li>
                    <li>Animated pulse effect for new notifications</li>
                    <li>Responsive design for all screen sizes</li>
                    <li>Accessible keyboard navigation support</li>
                  </ul>
                </div>
              </div>

              <div
                class="flex items-start p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <div
                  class="flex-shrink-0 w-8 h-8 flex items-center justify-center bg-blue-100 dark:bg-blue-900 rounded-full mr-4"
                >
                  <i class="fas fa-list text-blue-500"></i>
                </div>
                <div>
                  <h4 class="font-semibold mb-1">Notification Dropdown</h4>
                  <p class="text-sm">Click the bell to view:</p>
                  <ul class="mt-2 space-y-1 text-sm list-disc pl-4">
                    <li>List of recent notifications with timestamps</li>
                    <li>Notification type icons and priority indicators</li>
                    <li>Mark as read/unread toggle buttons</li>
                    <li>Quick action buttons for notification management</li>
                  </ul>
                </div>
              </div>

              <div
                class="flex items-start p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <div
                  class="flex-shrink-0 w-8 h-8 flex items-center justify-center bg-blue-100 dark:bg-blue-900 rounded-full mr-4"
                >
                  <i class="fas fa-filter text-blue-500"></i>
                </div>
                <div>
                  <h4 class="font-semibold mb-1">Notification Management</h4>
                  <p class="text-sm">Advanced features include:</p>
                  <ul class="mt-2 space-y-1 text-sm list-disc pl-4">
                    <li>Filter notifications by type, date, or status</li>
                    <li>Bulk mark as read/unread operations</li>
                    <li>Notification search and sorting capabilities</li>
                    <li>Archive and delete notification options</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- How to Use Notifications -->
          <h3 class="text-xl font-semibold mt-6 mb-4">
            How to Use Notifications
          </h3>
          <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm mb-6">
            <div class="space-y-6">
              <div
                class="flex items-start p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <div
                  class="flex-shrink-0 w-8 h-8 flex items-center justify-center bg-blue-100 dark:bg-blue-900 rounded-full mr-4"
                >
                  <span class="font-bold text-blue-500">1</span>
                </div>
                <div>
                  <h4 class="font-semibold mb-1">Accessing Notifications</h4>
                  <p class="text-sm">
                    Look for the bell icon 🔔 in the top navigation bar. The red
                    badge shows your unread notification count.
                  </p>
                </div>
              </div>

              <div
                class="flex items-start p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <div
                  class="flex-shrink-0 w-8 h-8 flex items-center justify-center bg-blue-100 dark:bg-blue-900 rounded-full mr-4"
                >
                  <span class="font-bold text-blue-500">2</span>
                </div>
                <div>
                  <h4 class="font-semibold mb-1">Viewing Notifications</h4>
                  <p class="text-sm">
                    Click the bell icon to open the notification dropdown and
                    see your recent alerts and messages.
                  </p>
                </div>
              </div>

              <div
                class="flex items-start p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <div
                  class="flex-shrink-0 w-8 h-8 flex items-center justify-center bg-blue-100 dark:bg-blue-900 rounded-full mr-4"
                >
                  <span class="font-bold text-blue-500">3</span>
                </div>
                <div>
                  <h4 class="font-semibold mb-1">Managing Notifications</h4>
                  <p class="text-sm">
                    Mark notifications as read/unread using the toggle buttons.
                    This helps you keep track of important updates.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Advanced Features Section -->
      <section id="advanced-features" class="scroll-mt-8">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Advanced Features
        </h2>
        <div class="text-gray-800 dark:text-gray-200 max-w-none">
          <!-- Statistics & Analytics -->
          <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm mb-6">
            <h3 class="text-xl font-semibold mb-4">Statistics & Analytics</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div class="p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
                <h4 class="font-semibold mb-2">Shipment Analytics</h4>
                <ul class="text-sm space-y-2">
                  <li>Monthly trends</li>
                  <li>Delivery performance</li>
                  <li>Volume analysis</li>
                </ul>
              </div>
              <div class="p-4 bg-green-50 dark:bg-green-900 rounded-lg">
                <h4 class="font-semibold mb-2">Inventory Reports</h4>
                <ul class="text-sm space-y-2">
                  <li>Stock levels</li>
                  <li>Location tracking</li>
                  <li>Movement history</li>
                </ul>
              </div>
              <div class="p-4 bg-purple-50 dark:bg-purple-900 rounded-lg">
                <h4 class="font-semibold mb-2">Custom Reports</h4>
                <ul class="text-sm space-y-2">
                  <li>Date range selection</li>
                  <li>Export options</li>
                  <li>Custom filters</li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Batch Operations -->
          <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm mb-6">
            <h3 class="text-xl font-semibold mb-4">Batch Operations</h3>
            <div class="space-y-4">
              <div class="flex items-start">
                <i class="fas fa-layer-group text-blue-500 mt-1 mr-3"></i>
                <div>
                  <h4 class="font-semibold">Multiple Lot Processing</h4>
                  <p class="text-sm">Process multiple lots simultaneously</p>
                </div>
              </div>
              <div class="flex items-start">
                <i class="fas fa-print text-blue-500 mt-1 mr-3"></i>
                <div>
                  <h4 class="font-semibold">Bulk Label Printing</h4>
                  <p class="text-sm">Print multiple labels in one operation</p>
                </div>
              </div>
              <div class="flex items-start">
                <i class="fas fa-file-export text-blue-500 mt-1 mr-3"></i>
                <div>
                  <h4 class="font-semibold">Mass Data Export</h4>
                  <p class="text-sm">Export data in various formats</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Tips & Tricks Section -->
      <section id="tips-and-tricks" class="scroll-mt-8">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Tips & Best Practices
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Performance Tips -->
          <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
            <h3 class="font-semibold mb-3">Performance Optimization</h3>
            <ul class="space-y-2 text-sm">
              <li class="flex items-start">
                <i class="fas fa-bolt text-yellow-500 mt-1 mr-2"></i>
                <span>Use batch processing for multiple wafers</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-clock text-yellow-500 mt-1 mr-2"></i>
                <span>Schedule large exports during off-peak hours</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-database text-yellow-500 mt-1 mr-2"></i>
                <span>Regular cache clearing improves performance</span>
              </li>
            </ul>
          </div>

          <!-- Security Best Practices -->
          <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
            <h3 class="font-semibold mb-3">Security Guidelines</h3>
            <ul class="space-y-2 text-sm">
              <li class="flex items-start">
                <i class="fas fa-shield-alt text-green-500 mt-1 mr-2"></i>
                <span>Regularly update Asana API tokens</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-lock text-green-500 mt-1 mr-2"></i>
                <span>Use secure printer connections</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-user-shield text-green-500 mt-1 mr-2"></i>
                <span>Maintain proper access permissions</span>
              </li>
            </ul>
          </div>
        </div>
      </section>

      <!-- Frequently Asked Questions Section -->
      <section id="faq" class="scroll-mt-8">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">
          Frequently Asked Questions
        </h2>
        <div class="text-gray-800 dark:text-gray-200 max-w-none">
          <p class="text-lg mb-8 text-gray-600 dark:text-gray-300">
            Find answers to common questions about the Talaria Dashboard system
            and its features.
          </p>

          <!-- General Questions -->
          <div class="space-y-6">
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
              <h3
                class="text-xl font-semibold mb-6 text-blue-600 dark:text-blue-400"
              >
                <i class="fas fa-info-circle mr-2"></i>General Questions
              </h3>

              <div class="space-y-6">
                <div class="border-l-4 border-blue-500 pl-4">
                  <h4 class="font-semibold text-lg mb-2">
                    What is Talaria Dashboard?
                  </h4>
                  <p class="text-gray-700 dark:text-gray-300">
                    Talaria Dashboard is a comprehensive wafer shipment
                    management system that integrates with Asana for task
                    management, provides inventory tracking, automated email
                    notifications, label generation, and real-time monitoring
                    capabilities. It's designed specifically for semiconductor
                    wafer logistics and quality control processes.
                  </p>
                </div>

                <div class="border-l-4 border-blue-500 pl-4">
                  <h4 class="font-semibold text-lg mb-2">
                    What are the main features of Talaria Dashboard?
                  </h4>
                  <div class="text-gray-700 dark:text-gray-300">
                    <p class="mb-3">Talaria Dashboard includes:</p>
                    <ul class="list-disc pl-6 space-y-2">
                      <li>
                        <strong>Asana Integration:</strong> Automated task
                        management and wafer information extraction
                      </li>
                      <li>
                        <strong>Inventory Management:</strong> Real-time wafer
                        tracking with location updates and bulk operations
                      </li>
                      <li>
                        <strong>Email Automation:</strong> Eiger shipping
                        notifications with preview and customization
                      </li>
                      <li>
                        <strong>RFQ System:</strong> Automated quotation request
                        emails with editable templates
                      </li>
                      <li>
                        <strong>Label Generation:</strong> Manual and automated
                        label creation for various shipment types
                      </li>
                      <li>
                        <strong>Smart Search:</strong> Advanced filtering and
                        search capabilities across all modules
                      </li>
                      <li>
                        <strong>Notification System:</strong> In-app
                        notifications and email alerts
                      </li>
                      <li>
                        <strong>Support System:</strong> Integrated help desk
                        and issue tracking
                      </li>
                    </ul>
                  </div>
                </div>

                <div class="border-l-4 border-blue-500 pl-4">
                  <h4 class="font-semibold text-lg mb-2">
                    Who can access Talaria Dashboard?
                  </h4>
                  <p class="text-gray-700 dark:text-gray-300">
                    Access is role-based with different permission levels. Admin
                    users have full access to all features including automation
                    settings, user management, and system configuration. Regular
                    users can access core functionality like inventory viewing,
                    label generation, and basic email features based on their
                    assigned permissions.
                  </p>
                </div>
              </div>
            </div>

            <!-- Asana Integration Questions -->
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
              <h3
                class="text-xl font-semibold mb-6 text-green-600 dark:text-green-400"
              >
                <i class="fas fa-link mr-2"></i>Asana Integration
              </h3>

              <div class="space-y-6">
                <div class="border-l-4 border-green-500 pl-4">
                  <h4 class="font-semibold text-lg mb-2">
                    How do I connect an Asana task to Talaria?
                  </h4>
                  <div class="text-gray-700 dark:text-gray-300">
                    <p class="mb-3">To connect an Asana task:</p>
                    <ol class="list-decimal pl-6 space-y-2">
                      <li>Copy the Asana task URL from your browser</li>
                      <li>
                        Paste it into the "Asana Task Link" field in any Talaria
                        module
                      </li>
                      <li>
                        The system will automatically extract wafer information
                        from custom fields
                      </li>
                      <li>
                        Verify the extracted data before proceeding with
                        operations
                      </li>
                    </ol>
                  </div>
                </div>

                <div class="border-l-4 border-green-500 pl-4">
                  <h4 class="font-semibold text-lg mb-2">
                    What Asana custom fields does Talaria use?
                  </h4>
                  <div class="text-gray-700 dark:text-gray-300">
                    <p class="mb-3">
                      Talaria extracts data from these Asana custom fields:
                    </p>
                    <ul class="list-disc pl-6 space-y-1">
                      <li>
                        <strong>Ligentec label title:</strong> Main shipment
                        identifier
                      </li>
                      <li>
                        <strong>Wafer IDs:</strong> List of wafers in the
                        shipment
                      </li>
                      <li>
                        <strong>Tracking number:</strong> Carrier tracking
                        information
                      </li>
                      <li>
                        <strong>Contact person:</strong> Customer contact email
                      </li>
                      <li>
                        <strong>Lot project:</strong> Project classification
                        (e.g., Eiger)
                      </li>
                      <li>
                        <strong>Manager acronym:</strong> Responsible manager
                        identifier
                      </li>
                    </ul>
                  </div>
                </div>

                <div class="border-l-4 border-green-500 pl-4">
                  <h4 class="font-semibold text-lg mb-2">
                    Why is my Asana task not loading data?
                  </h4>
                  <div class="text-gray-700 dark:text-gray-300">
                    <p class="mb-3">Common reasons and solutions:</p>
                    <ul class="list-disc pl-6 space-y-2">
                      <li>
                        <strong>Invalid URL format:</strong> Ensure you're using
                        the complete Asana task URL
                      </li>
                      <li>
                        <strong>Permission issues:</strong> Verify you have
                        access to the task in Asana
                      </li>
                      <li>
                        <strong>Missing custom fields:</strong> Check that
                        required custom fields are populated in Asana
                      </li>
                      <li>
                        <strong>API connectivity:</strong> Refresh the page and
                        try again
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <!-- Email & Notifications Questions -->
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
              <h3
                class="text-xl font-semibold mb-6 text-purple-600 dark:text-purple-400"
              >
                <i class="fas fa-envelope mr-2"></i>Email & Notifications
              </h3>

              <div class="space-y-6">
                <div class="border-l-4 border-purple-500 pl-4">
                  <h4 class="font-semibold text-lg mb-2">
                    How does the email preview feature work?
                  </h4>
                  <div class="text-gray-700 dark:text-gray-300">
                    <p class="mb-3">The email preview feature allows you to:</p>
                    <ul class="list-disc pl-6 space-y-2">
                      <li>Review email content before sending</li>
                      <li>Edit subject lines, recipients, and email body</li>
                      <li>
                        Send test emails to your own address for verification
                      </li>
                      <li>
                        Customize content while preserving automation data
                      </li>
                    </ul>
                    <p class="mt-3">
                      Simply click "Send Email" and the preview modal will open
                      automatically.
                    </p>
                  </div>
                </div>

                <div class="border-l-4 border-purple-500 pl-4">
                  <h4 class="font-semibold text-lg mb-2">
                    What is the difference between Eiger and non-Eiger
                    shipments?
                  </h4>
                  <p class="text-gray-700 dark:text-gray-300">
                    Eiger shipments are identified by the "Lot project" field
                    containing "Eiger" in Asana. These shipments follow specific
                    email templates and recipient lists. Non-Eiger shipments
                    will show a warning message but can still be processed with
                    appropriate notifications.
                  </p>
                </div>

                <div class="border-l-4 border-purple-500 pl-4">
                  <h4 class="font-semibold text-lg mb-2">
                    How do I set up RFQ automation emails?
                  </h4>
                  <div class="text-gray-700 dark:text-gray-300">
                    <p class="mb-3">To send RFQ emails:</p>
                    <ol class="list-decimal pl-6 space-y-2">
                      <li>
                        Navigate to the RFQ Automation tab in Label Generation
                      </li>
                      <li>Fill in the Sifo project and XFab Cloud URLs</li>
                      <li>Add project titles for each scope of work</li>
                      <li>
                        Use the preview feature to review and edit the email
                      </li>
                      <li>
                        Send test emails first, then production emails when
                        ready
                      </li>
                    </ol>
                  </div>
                </div>
              </div>
            </div>

            <!-- Inventory & Search Questions -->
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
              <h3
                class="text-xl font-semibold mb-6 text-orange-600 dark:text-orange-400"
              >
                <i class="fas fa-warehouse mr-2"></i>Inventory & Search
              </h3>

              <div class="space-y-6">
                <div class="border-l-4 border-orange-500 pl-4">
                  <h4 class="font-semibold text-lg mb-2">
                    How do I search for wafers in the inventory?
                  </h4>
                  <div class="text-gray-700 dark:text-gray-300">
                    <p class="mb-3">Talaria offers multiple search options:</p>
                    <ul class="list-disc pl-6 space-y-2">
                      <li>
                        <strong>Quick Search:</strong> Use the search bar to
                        find wafers by ID, lot, or location
                      </li>
                      <li>
                        <strong>Filter Buttons:</strong> Use Recent Arrivals,
                        Recently Shipped, Available Stock, etc.
                      </li>
                      <li>
                        <strong>Advanced Filters:</strong> Filter by date
                        ranges, locations, or specific criteria
                      </li>
                      <li>
                        <strong>Keyboard Shortcuts:</strong> Press Ctrl+K for
                        quick search, Enter to execute
                      </li>
                    </ul>
                  </div>
                </div>

                <div class="border-l-4 border-orange-500 pl-4">
                  <h4 class="font-semibold text-lg mb-2">
                    Can I modify multiple wafers at once?
                  </h4>
                  <p class="text-gray-700 dark:text-gray-300">
                    Yes! Talaria supports bulk operations. Select multiple
                    wafers using checkboxes, then use the bulk modification
                    features to update locations, lot IDs, or other common
                    attributes simultaneously. This is especially useful when
                    multiple wafers share the same characteristics.
                  </p>
                </div>

                <div class="border-l-4 border-orange-500 pl-4">
                  <h4 class="font-semibold text-lg mb-2">
                    How does the smart automation work?
                  </h4>
                  <div class="text-gray-700 dark:text-gray-300">
                    <p class="mb-3">Smart automation features include:</p>
                    <ul class="list-disc pl-6 space-y-2">
                      <li>
                        <strong>Duplicate Prevention:</strong> Checks for
                        existing wafers before adding new ones
                      </li>
                      <li>
                        <strong>Real-time Notifications:</strong> Alerts when
                        wafers are added to Icarium DB
                      </li>
                      <li>
                        <strong>Workflow Triggers:</strong> Automated actions
                        based on inventory changes
                      </li>
                      <li>
                        <strong>Integration Monitoring:</strong> Continuous sync
                        with external systems
                      </li>
                    </ul>
                    <p class="mt-3 text-sm text-gray-600 dark:text-gray-400">
                      Note: Automation features are restricted to admin users
                      only.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Technical & System Questions -->
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
              <h3
                class="text-xl font-semibold mb-6 text-red-600 dark:text-red-400"
              >
                <i class="fas fa-cogs mr-2"></i>Technical & System
              </h3>

              <div class="space-y-6">
                <div class="border-l-4 border-red-500 pl-4">
                  <h4 class="font-semibold text-lg mb-2">
                    What browsers are supported?
                  </h4>
                  <p class="text-gray-700 dark:text-gray-300">
                    Talaria Dashboard works best with modern browsers including
                    Chrome, Firefox, Safari, and Edge. For optimal performance,
                    ensure JavaScript is enabled and your browser is up to date.
                  </p>
                </div>

                <div class="border-l-4 border-red-500 pl-4">
                  <h4 class="font-semibold text-lg mb-2">
                    How do I report issues or request features?
                  </h4>
                  <div class="text-gray-700 dark:text-gray-300">
                    <p class="mb-3">You can get support through:</p>
                    <ul class="list-disc pl-6 space-y-2">
                      <li>
                        <strong>Support Page:</strong> Use the red "Support"
                        link in the navigation
                      </li>
                      <li>
                        <strong>Email:</strong> Contact <EMAIL>
                        directly
                      </li>
                      <li>
                        <strong>In-app Chat:</strong> Use the chat interface for
                        immediate assistance
                      </li>
                      <li>
                        <strong>Issue Tracking:</strong> Submit detailed bug
                        reports or enhancement requests
                      </li>
                    </ul>
                  </div>
                </div>

                <div class="border-l-4 border-red-500 pl-4">
                  <h4 class="font-semibold text-lg mb-2">Is my data secure?</h4>
                  <p class="text-gray-700 dark:text-gray-300">
                    Yes. Talaria Dashboard implements role-based access control,
                    secure database connections, and follows industry best
                    practices for data protection. All sensitive operations
                    require appropriate permissions, and audit trails are
                    maintained for critical actions.
                  </p>
                </div>

                <div class="border-l-4 border-red-500 pl-4">
                  <h4 class="font-semibold text-lg mb-2">
                    Can I integrate Talaria with other systems?
                  </h4>
                  <div class="text-gray-700 dark:text-gray-300">
                    <p class="mb-3">Talaria already integrates with:</p>
                    <ul class="list-disc pl-6 space-y-1">
                      <li>
                        <strong>Asana:</strong> Task management and data
                        extraction
                      </li>
                      <li>
                        <strong>Icarium DB:</strong> Wafer database
                        synchronization
                      </li>
                      <li>
                        <strong>ODOO ERP:</strong> Enterprise resource planning
                      </li>
                      <li>
                        <strong>Email Systems:</strong> SMTP for notifications
                      </li>
                    </ul>
                    <p class="mt-3">
                      Additional integrations can be developed based on business
                      requirements.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Troubleshooting Section -->
      <section id="troubleshooting" class="scroll-mt-8">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Troubleshooting Guide
        </h2>
        <div class="text-gray-800 dark:text-gray-200 max-w-none">
          <p class="text-lg mb-8 text-gray-600 dark:text-gray-300">
            Common issues and solutions for Talaria Dashboard users.
          </p>

          <!-- Common Issues -->
          <div class="space-y-6">
            <!-- Email Issues -->
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
              <h3
                class="text-xl font-semibold mb-4 text-purple-600 dark:text-purple-400"
              >
                <i class="fas fa-envelope mr-2"></i>Email Issues
              </h3>
              <div class="space-y-4">
                <div class="p-4 bg-red-50 dark:bg-red-900 rounded-lg">
                  <h4 class="font-semibold text-red-600 dark:text-red-300">
                    Email Preview Not Loading
                  </h4>
                  <p class="text-sm mt-2">Solutions:</p>
                  <ul class="list-disc pl-5 text-sm mt-1 space-y-1">
                    <li>Ensure Asana task link is properly entered</li>
                    <li>Verify task contains required custom fields</li>
                    <li>Check internet connection and refresh page</li>
                    <li>Confirm you have access to the Asana task</li>
                  </ul>
                </div>
                <div class="p-4 bg-yellow-50 dark:bg-yellow-900 rounded-lg">
                  <h4
                    class="font-semibold text-yellow-600 dark:text-yellow-300"
                  >
                    Email Not Sending
                  </h4>
                  <p class="text-sm mt-2">Solutions:</p>
                  <ul class="list-disc pl-5 text-sm mt-1 space-y-1">
                    <li>Use test email mode first to verify functionality</li>
                    <li>Check recipient email addresses for typos</li>
                    <li>Verify email server configuration</li>
                    <li>Contact support if issue persists</li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- System Issues -->
            <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
              <h3
                class="text-xl font-semibold mb-4 text-blue-600 dark:text-blue-400"
              >
                <i class="fas fa-desktop mr-2"></i>System Issues
              </h3>
              <div class="overflow-hidden">
                <table class="min-w-full">
                  <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th
                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase"
                      >
                        Issue
                      </th>
                      <th
                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase"
                      >
                        Solution
                      </th>
                    </tr>
                  </thead>
                  <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    <tr>
                      <td class="px-6 py-4 text-sm">
                        Inventory data not loading
                      </td>
                      <td class="px-6 py-4 text-sm">
                        Check database connection, refresh page, verify
                        permissions
                      </td>
                    </tr>
                    <tr>
                      <td class="px-6 py-4 text-sm">Asana integration error</td>
                      <td class="px-6 py-4 text-sm">
                        Verify task URL format, check API token, confirm task
                        access
                      </td>
                    </tr>
                    <tr>
                      <td class="px-6 py-4 text-sm">Search not working</td>
                      <td class="px-6 py-4 text-sm">
                        Clear browser cache, check search syntax, try different
                        filters
                      </td>
                    </tr>
                    <tr>
                      <td class="px-6 py-4 text-sm">Bulk operations failing</td>
                      <td class="px-6 py-4 text-sm">
                        Reduce selection size, check individual wafer
                        permissions
                      </td>
                    </tr>
                    <tr>
                      <td class="px-6 py-4 text-sm">
                        Notifications not appearing
                      </td>
                      <td class="px-6 py-4 text-sm">
                        Enable browser notifications, check notification
                        settings
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- Common Scenarios -->
          <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm mt-6">
            <h3
              class="text-xl font-semibold mb-4 text-green-600 dark:text-green-400"
            >
              <i class="fas fa-lightbulb mr-2"></i>Common Scenarios & Solutions
            </h3>
            <div class="grid grid-cols-1 gap-4">
              <!-- Email Preview Issues -->
              <div
                class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <h4 class="font-semibold text-purple-600 dark:text-purple-400">
                  Email Preview & Sending Issues
                </h4>
                <div class="mt-2 space-y-3">
                  <div class="ml-4">
                    <p class="font-medium text-sm">Preview Modal Won't Open:</p>
                    <ul class="list-disc pl-5 text-sm mt-1 space-y-1">
                      <li>Ensure Asana task link is entered correctly</li>
                      <li>Check that task contains required custom fields</li>
                      <li>Verify you have access to the Asana task</li>
                      <li>Refresh page and try again</li>
                    </ul>
                  </div>
                  <div class="ml-4">
                    <p class="font-medium text-sm">Test Emails Not Received:</p>
                    <ul class="list-disc pl-5 text-sm mt-1 space-y-1">
                      <li>Check spam/junk folder</li>
                      <li>Verify test email address is correct</li>
                      <li>Wait a few minutes for delivery</li>
                      <li>Contact support if issue persists</li>
                    </ul>
                  </div>
                </div>
              </div>

              <!-- Inventory Management Issues -->
              <div
                class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <h4 class="font-semibold text-orange-600 dark:text-orange-400">
                  Inventory Management Issues
                </h4>
                <div class="mt-2 space-y-3">
                  <div class="ml-4">
                    <p class="font-medium text-sm">Search Results Empty:</p>
                    <ul class="list-disc pl-5 text-sm mt-1 space-y-1">
                      <li>Check search terms for typos</li>
                      <li>Try broader search criteria</li>
                      <li>Clear all filters and search again</li>
                      <li>Verify database connection</li>
                    </ul>
                  </div>
                  <div class="ml-4">
                    <p class="font-medium text-sm">Bulk Operations Failing:</p>
                    <ul class="list-disc pl-5 text-sm mt-1 space-y-1">
                      <li>Reduce number of selected items</li>
                      <li>Check individual wafer permissions</li>
                      <li>Verify all selected wafers are valid</li>
                      <li>Try updating in smaller batches</li>
                    </ul>
                  </div>
                </div>
              </div>

              <!-- RFQ & Automation Issues -->
              <div
                class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <h4 class="font-semibold text-blue-600 dark:text-blue-400">
                  RFQ & Automation Issues
                </h4>
                <div class="mt-2 space-y-3">
                  <div class="ml-4">
                    <p class="font-medium text-sm">RFQ Email Not Sending:</p>
                    <ul class="list-disc pl-5 text-sm mt-1 space-y-1">
                      <li>Fill all required fields (Sifo project, URLs)</li>
                      <li>Use preview mode to verify content</li>
                      <li>Send test email first</li>
                      <li>Check recipient email addresses</li>
                    </ul>
                  </div>
                  <div class="ml-4">
                    <p class="font-medium text-sm">
                      Automation Features Not Working:
                    </p>
                    <ul class="list-disc pl-5 text-sm mt-1 space-y-1">
                      <li>Verify admin permissions</li>
                      <li>Check system integrations (Asana, ODOO)</li>
                      <li>Review automation rules configuration</li>
                      <li>Contact support for advanced troubleshooting</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Support Contact -->
          <div class="mt-6 bg-blue-50 dark:bg-blue-900 p-4 rounded-lg">
            <h4 class="font-semibold mb-2">Need Additional Help?</h4>
            <p class="text-sm">Contact support:</p>
            <ul class="mt-2 space-y-2 text-sm">
              <li class="flex items-center">
                <i class="fas fa-envelope text-blue-500 mr-2"></i>
                <span><EMAIL>/<EMAIL></span>
              </li>
              <li class="flex items-center">
                <i class="fas fa-phone text-blue-500 mr-2"></i>
                <span>+33 7 76 02 67 88</span>
              </li>
            </ul>
          </div>
        </div>
      </section>
    </div>
  </div>
</div>
{% endblock %} {% block scripts %}
<script>
  // Polyfill for Cache API to prevent "caches is not defined" error
  if (typeof window.caches === "undefined") {
    window.caches = {
      open: () =>
        Promise.resolve({
          put: () => Promise.resolve(),
          match: () => Promise.resolve(null),
          delete: () => Promise.resolve(false),
        }),
      delete: () => Promise.resolve(false),
      has: () => Promise.resolve(false),
      keys: () => Promise.resolve([]),
    };
    console.log("Cache API polyfill installed");
  }

  document.addEventListener("DOMContentLoaded", function () {
    // Interactive Navigation
    const navItems = document.querySelectorAll("#doc-nav a");

    // Modern Search Implementation - define all UI elements first
    const searchInput = document.getElementById("modern-search");
    const searchStatus = document.getElementById("search-status");
    const clearSearchBtn = document.getElementById("clear-search");
    const searchSummary = document.getElementById("search-summary");
    const resultsText = document.getElementById("results-text");
    const clearResultsBtn = document.getElementById("clear-results");
    const searchSuggestions = document.getElementById("search-suggestions");
    const suggestionButtons = document.querySelectorAll(".search-suggestion");

    // Initialize search state variables
    let originalSectionContent = new Map();
    let isSearchActive = false;

    console.log("Search input element found:", searchInput !== null);

    // Get all searchable content sections - try multiple selectors to ensure we find content
    let sections = [];

    // Try different selectors to find searchable content
    const docSections = document.querySelectorAll(".doc-section");
    const sectionElements = document.querySelectorAll("section");
    const contentDivs = document.querySelectorAll(
      "#documentation-content > div, .documentation-content > div"
    );
    const mainContent = document.querySelector(".col-span-4, .col-span-9");

    // Choose the most specific selector that returns results
    if (docSections.length) {
      sections = docSections;
      console.log("Using .doc-section elements:", sections.length);
    } else if (sectionElements.length) {
      sections = sectionElements;
      console.log("Using section elements:", sections.length);
    } else if (contentDivs.length) {
      sections = contentDivs;
      console.log("Using documentation content divs:", sections.length);
    } else if (mainContent) {
      // If all else fails, use the main content area's direct children
      sections = Array.from(mainContent.children);
      console.log("Using main content children as sections:", sections.length);
    }

    // If we still don't have sections, create a warning
    if (!sections.length) {
      console.error(
        "WARNING: No searchable sections found. Search functionality may not work correctly."
      );
      // Add a warning to the page
      if (searchInput) {
        const searchContainer = searchInput.closest(".relative");
        if (searchContainer) {
          const warning = document.createElement("div");
          warning.className = "mt-2 text-red-500 text-sm";
          warning.textContent =
            "Search initialization issue: No content sections found to search.";
          searchContainer.after(warning);
        }
      }
    }

    // Store original content for each section
    sections.forEach((section, index) => {
      // If section doesn't have an ID, generate one
      if (!section.id) {
        section.id = `doc-section-${index}`;
        console.log(`Assigned ID ${section.id} to section without ID`);
      }
      originalSectionContent.set(section.id, section.innerHTML);
    });

    // Modern Search Functions
    function showSearchStatus() {
      searchStatus.classList.remove("hidden");
      clearSearchBtn.classList.add("hidden");
    }

    function hideSearchStatus() {
      searchStatus.classList.add("hidden");
      if (searchInput.value.trim()) {
        clearSearchBtn.classList.remove("hidden");
      }
    }

    function showClearButton() {
      clearSearchBtn.classList.remove("hidden");
    }

    function hideClearButton() {
      clearSearchBtn.classList.add("hidden");
    }

    function showSearchSummary(visibleCount, totalCount, searchTerm) {
      resultsText.textContent = `Found ${visibleCount} of ${totalCount} sections matching "${searchTerm}"`;
      searchSummary.classList.remove("hidden");
      searchSuggestions.classList.add("hidden");
    }

    function hideSearchSummary() {
      searchSummary.classList.add("hidden");
      searchSuggestions.classList.remove("hidden");
    }

    function clearAllSearch() {
      console.log("Clearing search");
      isSearchActive = false;

      // First approach: Clear sections if we have them
      if (sections.length > 0) {
        sections.forEach((section) => {
          section.style.display = "block";
          section.classList.remove("search-dimmed");
          // Restore original content
          if (originalSectionContent.has(section.id)) {
            section.innerHTML = originalSectionContent.get(section.id);
          }
        });
      }
      // Second approach: Clear content elements if no sections
      else {
        // Remove all highlights from the document
        const highlights = document.querySelectorAll(".search-highlight");
        highlights.forEach((highlight) => {
          const parent = highlight.parentNode;
          parent.replaceChild(
            document.createTextNode(highlight.textContent),
            highlight
          );
          parent.normalize(); // Merge adjacent text nodes
        });

        // Make all content elements visible again
        const allElements = document.querySelectorAll("*");
        allElements.forEach((element) => {
          // Only reset display for elements that might have been hidden during search
          if (element.style.display === "none") {
            element.style.display = "";
          }
        });
      }

      hideSearchSummary();
      hideClearButton();
      searchInput.value = "";
      searchInput.focus();
    }

    function highlightText(element, searchTerm) {
      if (!searchTerm || searchTerm.length < 2) return;

      const regex = new RegExp(
        `(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")})`,
        "gi"
      );

      const walker = document.createTreeWalker(
        element,
        NodeFilter.SHOW_TEXT,
        {
          acceptNode: function (node) {
            // Skip script tags and already highlighted content
            if (
              node.parentNode.tagName === "SCRIPT" ||
              node.parentNode.tagName === "MARK" ||
              node.parentNode.classList?.contains("search-highlight")
            ) {
              return NodeFilter.FILTER_REJECT;
            }
            return NodeFilter.FILTER_ACCEPT;
          },
        },
        false
      );

      const textNodes = [];
      let node;
      while ((node = walker.nextNode())) {
        if (node.nodeValue.trim()) {
          textNodes.push(node);
        }
      }

      textNodes.forEach((textNode) => {
        const text = textNode.nodeValue;
        if (regex.test(text)) {
          const highlightedText = text.replace(
            regex,
            '<mark class="search-highlight bg-yellow-300 dark:bg-yellow-600 px-1 py-0.5 rounded-sm font-medium">$1</mark>'
          );
          const wrapper = document.createElement("span");
          wrapper.innerHTML = highlightedText;
          textNode.parentNode.replaceChild(wrapper, textNode);
        }
      });
    }

    function performSearch(searchTerm) {
      console.log("Performing search for:", searchTerm);

      if (!searchTerm || searchTerm.length < 2) {
        console.log("Search term too short, clearing search");
        clearAllSearch();
        return;
      }

      showSearchStatus();
      isSearchActive = true;
      const searchLower = searchTerm.toLowerCase();
      let visibleCount = 0;
      const totalCount = sections.length;
      console.log("Sections to search:", totalCount);

      // First approach: If we have sections, search through them
      if (sections.length > 0) {
        sections.forEach((section) => {
          // Restore original content first
          if (originalSectionContent.has(section.id)) {
            section.innerHTML = originalSectionContent.get(section.id);
          }

          const content = section.textContent.toLowerCase();
          const isVisible = content.includes(searchLower);

          if (isVisible) {
            visibleCount++;
            section.style.display = "block";
            section.classList.remove("search-dimmed");
            highlightText(section, searchTerm);
            console.log("Match found in section:", section.id);
          } else {
            section.style.display = "none";
            section.classList.add("search-dimmed");
          }
        });
      }
      // Second approach: If no sections, search through all page content
      else {
        console.log("Fallback: Searching all page content");
        // Get the main content container
        const mainContent =
          document.querySelector("#main-content") || document.body;

        // Get all paragraphs, headings, lists, etc.
        const contentElements = mainContent.querySelectorAll(
          "p, h1, h2, h3, h4, h5, h6, li, td, th, code"
        );
        const totalElements = contentElements.length;
        console.log("Searching through", totalElements, "content elements");

        // Search through all content elements
        contentElements.forEach((element) => {
          const content = element.textContent.toLowerCase();
          const isVisible = content.includes(searchLower);

          if (isVisible) {
            visibleCount++;
            // Make parent elements visible
            let parent = element.parentElement;
            while (parent && parent !== mainContent) {
              parent.style.display = "block";
              parent = parent.parentElement;
            }

            // Highlight the matching text
            highlightText(element, searchTerm);
            console.log("Match found in element:", element.tagName);

            // Scroll to the first match
            if (visibleCount === 1) {
              element.scrollIntoView({ behavior: "smooth", block: "center" });
            }
          }
        });
      }

      console.log("Search complete. Found", visibleCount, "matches");
      hideSearchStatus();
      showClearButton();
      showSearchSummary(visibleCount, totalCount || 0, searchTerm);

      // Scroll to first visible section if any (only needed for section-based search)
      if (visibleCount > 0 && sections.length > 0) {
        const firstVisible = Array.from(sections).find(
          (s) => s.style.display !== "none"
        );
        if (firstVisible) {
          firstVisible.scrollIntoView({ behavior: "smooth", block: "start" });
          console.log("Scrolled to first visible section:", firstVisible.id);
        }
      }
    }

    // Debounce function for search performance
    function debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    }

    // Search Event Listeners
    if (searchInput) {
      console.log("Search input found, setting up event listeners");

      // Real-time search with debounce
      searchInput.addEventListener(
        "input",
        debounce(function (e) {
          const searchTerm = e.target.value.trim();
          console.log("Input event triggered with term:", searchTerm);
          performSearch(searchTerm);
        }, 250)
      );

      // Handle Enter key for immediate search
      searchInput.addEventListener("keydown", function (e) {
        console.log("Key pressed:", e.key);
        if (e.key === "Enter") {
          e.preventDefault();
          console.log("Enter key pressed, performing immediate search");
          const searchTerm = this.value.trim();
          performSearch(searchTerm);
        }
        if (e.key === "Escape") {
          console.log("Escape key pressed, clearing search");
          clearAllSearch();
        }
      });

      // Focus search input on page load
      searchInput.focus();
      console.log("Search input focused");
    } else {
      console.error(
        "Search input element not found! Check if the ID 'modern-search' exists in the HTML."
      );
    }

    // Clear search button
    if (clearSearchBtn) {
      clearSearchBtn.addEventListener("click", clearAllSearch);
    }

    // Clear results button
    if (clearResultsBtn) {
      clearResultsBtn.addEventListener("click", clearAllSearch);
    }

    // Search suggestion buttons
    suggestionButtons.forEach((button) => {
      button.addEventListener("click", function () {
        const term = this.getAttribute("data-term");
        searchInput.value = term;
        performSearch(term);
        searchInput.focus();
      });
    });

    // Intersection Observer for active section highlighting
    const observerOptions = {
      root: null,
      rootMargin: "-20% 0px -60% 0px",
      threshold: 0,
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          // Update navigation
          navItems.forEach((item) => {
            const href = item.getAttribute("href").substring(1);
            if (href === entry.target.id) {
              item.classList.add(
                "bg-blue-50",
                "text-blue-700",
                "dark:bg-blue-900",
                "dark:text-blue-200"
              );
            } else {
              item.classList.remove(
                "bg-blue-50",
                "text-blue-700",
                "dark:bg-blue-900",
                "dark:text-blue-200"
              );
            }
          });

          // Update URL hash without scrolling
          const currentHash = window.location.hash;
          const newHash = `#${entry.target.id}`;
          if (currentHash !== newHash) {
            history.replaceState(null, null, newHash);
          }
        }
      });
    }, observerOptions);

    sections.forEach((section) => observer.observe(section));

    // Smooth scrolling with offset
    navItems.forEach((item) => {
      item.addEventListener("click", (e) => {
        e.preventDefault();
        const targetId = item.getAttribute("href").substring(1);
        const targetSection = document.getElementById(targetId);

        if (targetSection) {
          const offset = 80; // Adjust based on your header height
          const targetPosition =
            targetSection.getBoundingClientRect().top +
            window.pageYOffset -
            offset;

          window.scrollTo({
            top: targetPosition,
            behavior: "smooth",
          });
        }
      });
    });

    // Interactive code snippets
    const codeBlocks = document.querySelectorAll("pre code");
    codeBlocks.forEach((block) => {
      // Add copy button
      const copyButton = document.createElement("button");
      copyButton.innerHTML = '<i class="fas fa-copy"></i>';
      copyButton.className =
        "absolute top-2 right-2 p-2 rounded bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600";

      copyButton.addEventListener("click", async () => {
        try {
          await navigator.clipboard.writeText(block.textContent);
          copyButton.innerHTML = '<i class="fas fa-check text-green-500"></i>';
          setTimeout(() => {
            copyButton.innerHTML = '<i class="fas fa-copy"></i>';
          }, 2000);
        } catch (err) {
          console.error("Failed to copy:", err);
        }
      });

      const wrapper = document.createElement("div");
      wrapper.className = "relative";
      block.parentNode.insertBefore(wrapper, block);
      wrapper.appendChild(block);
      wrapper.appendChild(copyButton);
    });

    // Utility functions
    function debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    }

    // Add expand/collapse functionality for sidebar sections
    const sectionHeaders = document.querySelectorAll(".section-header");
    sectionHeaders.forEach((header) => {
      header.addEventListener("click", () => {
        const content = header.nextElementSibling;
        content.style.maxHeight = content.style.maxHeight
          ? null
          : content.scrollHeight + "px";
        header.classList.toggle("expanded");
      });
    });

    // Print functionality
    const printButtons = document.querySelectorAll(".print-section");
    printButtons.forEach((button) => {
      button.addEventListener("click", () => {
        const sectionId = button.getAttribute("data-section");
        const section = document.getElementById(sectionId);
        if (section) {
          const printWindow = window.open("", "_blank");
          printWindow.document.write(`
                    <html>
                        <head>
                            <title>Print Documentation</title>
                            <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
                        </head>
                        <body class="p-8">
                            ${section.innerHTML}
                        </body>
                    </html>
                `);
          printWindow.document.close();
          printWindow.print();
        }
      });
    });
  });
</script>
{% endblock %}
