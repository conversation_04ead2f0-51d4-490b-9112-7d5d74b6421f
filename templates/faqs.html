{% extends "base.html" %} {% block title %}FAQs - Talaria Dashboard{% endblock
%} {% block content %}
<div
  class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800"
>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Enhanced Header -->
    <div class="text-center mb-12">
      <div
        class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full mb-6"
      >
        <i class="fas fa-question-circle text-white text-2xl"></i>
      </div>
      <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
        Frequently Asked Questions
      </h1>
      <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
        Find comprehensive answers to common questions about the Talaria
        Dashboard system, its powerful features, and best practices for wafer
        management workflows
      </p>
    </div>

    <!-- Enhanced Search Box -->
    <div class="mb-12">
      <div class="max-w-2xl mx-auto">
        <div class="relative">
          <input
            type="text"
            id="faqSearch"
            class="w-full px-6 py-4 text-lg border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 dark:bg-gray-800 dark:text-white shadow-lg transition-all duration-200"
            placeholder="Search FAQs... (e.g., 'Asana integration', 'email preview', 'inventory search')"
          />
          <div class="absolute right-4 top-1/2 transform -translate-y-1/2">
            <i class="fas fa-search text-gray-400 text-xl"></i>
          </div>
          <div class="absolute left-4 top-1/2 transform -translate-y-1/2">
            <kbd
              class="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg dark:bg-gray-600 dark:text-gray-100 dark:border-gray-500"
            >
              Ctrl+K
            </kbd>
          </div>
        </div>
        <!-- Search Results Counter -->
        <div
          id="searchResults"
          class="mt-3 text-center text-sm text-gray-500 dark:text-gray-400 hidden"
        >
          <span id="resultCount">0</span> results found
        </div>
      </div>
    </div>

    <!-- Quick Filter Tags -->
    <div class="mb-8">
      <div class="flex flex-wrap justify-center gap-3">
        <button
          class="filter-tag px-4 py-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full border border-gray-200 dark:border-gray-600 hover:bg-blue-50 dark:hover:bg-gray-700 transition-colors"
          data-filter="all"
        >
          <i class="fas fa-list mr-2"></i>All Topics
        </button>
        <button
          class="filter-tag px-4 py-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full border border-gray-200 dark:border-gray-600 hover:bg-blue-50 dark:hover:bg-gray-700 transition-colors"
          data-filter="general"
        >
          <i class="fas fa-info-circle mr-2"></i>General
        </button>
        <button
          class="filter-tag px-4 py-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full border border-gray-200 dark:border-gray-600 hover:bg-blue-50 dark:hover:bg-gray-700 transition-colors"
          data-filter="asana"
        >
          <i class="fab fa-asana mr-2"></i>Asana
        </button>
        <button
          class="filter-tag px-4 py-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full border border-gray-200 dark:border-gray-600 hover:bg-blue-50 dark:hover:bg-gray-700 transition-colors"
          data-filter="email"
        >
          <i class="fas fa-envelope mr-2"></i>Email
        </button>
        <button
          class="filter-tag px-4 py-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full border border-gray-200 dark:border-gray-600 hover:bg-blue-50 dark:hover:bg-gray-700 transition-colors"
          data-filter="inventory"
        >
          <i class="fas fa-boxes mr-2"></i>Inventory
        </button>
        <button
          class="filter-tag px-4 py-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full border border-gray-200 dark:border-gray-600 hover:bg-blue-50 dark:hover:bg-gray-700 transition-colors"
          data-filter="technical"
        >
          <i class="fas fa-cogs mr-2"></i>Technical
        </button>
      </div>
    </div>

    <!-- FAQ Categories -->
    <div class="space-y-8" id="faqContainer">
      {% for category, content in faqs.items() %}
      <div
        class="faq-category bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden border border-gray-100 dark:border-gray-700"
        data-category="{{ category }}"
      >
        <!-- Category Header -->
        <div
          class="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-600 px-8 py-6 border-b border-gray-200 dark:border-gray-600"
        >
          <div class="flex items-center">
            <div
              class="w-12 h-12 rounded-xl bg-gradient-to-r {% if category == 'general' %}from-blue-500 to-blue-600{% endif %} {% if category == 'asana' %}from-purple-500 to-purple-600{% endif %} {% if category == 'email' %}from-green-500 to-green-600{% endif %} {% if category == 'inventory' %}from-orange-500 to-orange-600{% endif %} {% if category == 'technical' %}from-red-500 to-red-600{% endif %} flex items-center justify-center mr-4"
            >
              <i
                class="text-white text-xl {% if category == 'general' %}fas fa-info-circle{% endif %} {% if category == 'asana' %}fab fa-asana{% endif %} {% if category == 'email' %}fas fa-envelope{% endif %} {% if category == 'inventory' %}fas fa-boxes{% endif %} {% if category == 'technical' %}fas fa-cogs{% endif %}"
              ></i>
            </div>
            <div>
              <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
                {{ content.title }}
              </h2>
              <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {% if category == 'general' %}Essential information about
                Talaria Dashboard{% endif %} {% if category == 'asana'
                %}Integration setup and task management{% endif %} {% if
                category == 'email' %}Email automation and notification
                features{% endif %} {% if category == 'inventory' %}Wafer
                tracking and search capabilities{% endif %} {% if category ==
                'technical' %}Troubleshooting and technical support{% endif %}
              </p>
            </div>
          </div>
        </div>

        <!-- Questions Container -->
        <div class="p-8">
          <div class="space-y-6">
            {% for item in content.questions %}
            <div
              class="faq-item border border-gray-200 dark:border-gray-600 rounded-xl overflow-hidden hover:shadow-lg transition-all duration-200"
              data-category="{{ category }}"
            >
              <button
                class="faq-question w-full text-left focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset"
              >
                <div
                  class="flex items-center justify-between p-6 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                >
                  <h3
                    class="text-lg font-semibold text-gray-900 dark:text-white pr-4"
                  >
                    {{ item.question }}
                  </h3>
                  <span
                    class="ml-6 h-8 w-8 flex items-center justify-center bg-white dark:bg-gray-800 rounded-full shadow-sm"
                  >
                    <i
                      class="fas fa-chevron-down transform transition-transform text-gray-500 dark:text-gray-400"
                    ></i>
                  </span>
                </div>
              </button>
              <div class="faq-answer hidden">
                <div
                  class="p-6 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-600"
                >
                  <div class="prose prose-gray dark:prose-invert max-w-none">
                    <p class="text-gray-700 dark:text-gray-300 leading-relaxed">
                      {{ item.answer }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
            {% endfor %}
          </div>
        </div>
      </div>
      {% endfor %}
    </div>

    <!-- No Results Message -->
    <div id="noResults" class="hidden text-center py-16">
      <div
        class="inline-flex items-center justify-center w-20 h-20 bg-gray-100 dark:bg-gray-700 rounded-full mb-6"
      >
        <i class="fas fa-search text-gray-400 text-2xl"></i>
      </div>
      <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
        No results found
      </h3>
      <p class="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
        Try adjusting your search terms or browse through the categories above
        to find what you're looking for.
      </p>
    </div>

    <!-- Enhanced Contact Support Section -->
    <div
      class="mt-16 bg-gradient-to-r from-blue-600 to-indigo-700 rounded-2xl p-8 text-white"
    >
      <div class="text-center">
        <div
          class="inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mb-6"
        >
          <i class="fas fa-headset text-2xl"></i>
        </div>
        <h3 class="text-2xl font-bold mb-4">Still have questions?</h3>
        <p class="text-blue-100 mb-8 max-w-2xl mx-auto">
          Our expert support team is here to help you get the most out of
          Talaria Dashboard. Get personalized assistance with your wafer
          management workflows.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a
            href="mailto:<EMAIL>"
            class="inline-flex items-center px-6 py-3 bg-white text-blue-600 rounded-xl hover:bg-gray-100 transition-colors font-semibold"
          >
            <i class="fas fa-envelope mr-2"></i>
            Email Support
          </a>
          <a
            href="{{ url_for('support') }}"
            class="inline-flex items-center px-6 py-3 bg-white/20 text-white rounded-xl hover:bg-white/30 transition-colors font-semibold border border-white/30"
          >
            <i class="fas fa-ticket-alt mr-2"></i>
            Submit Ticket
          </a>
        </div>
      </div>
    </div>

    <!-- Additional Resources -->
    <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
      <div
        class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-100 dark:border-gray-700"
      >
        <div
          class="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mb-4"
        >
          <i class="fas fa-book text-white"></i>
        </div>
        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          Documentation
        </h4>
        <p class="text-gray-600 dark:text-gray-400 text-sm mb-4">
          Comprehensive guides and tutorials for all Talaria features
        </p>
        <a
          href="{{ url_for('documentation') }}"
          class="text-purple-600 dark:text-purple-400 font-medium hover:underline"
        >
          Browse Docs →
        </a>
      </div>

      <div
        class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-100 dark:border-gray-700"
      >
        <div
          class="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center mb-4"
        >
          <i class="fas fa-comments text-white"></i>
        </div>
        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          Live Chat
        </h4>
        <p class="text-gray-600 dark:text-gray-400 text-sm mb-4">
          Get instant help from our support team via live chat
        </p>
        <button
          onclick="openLiveChat()"
          class="text-green-600 dark:text-green-400 font-medium hover:underline"
        >
          Start Chat →
        </button>
      </div>

      <div
        class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-100 dark:border-gray-700"
      >
        <div
          class="w-12 h-12 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg flex items-center justify-center mb-4"
        >
          <i class="fas fa-video text-white"></i>
        </div>
        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          Video Tutorials
        </h4>
        <p class="text-gray-600 dark:text-gray-400 text-sm mb-4">
          Watch step-by-step video guides for common workflows
        </p>
        <a
          href="{{ url_for('video_tutorials') }}"
          class="text-orange-600 dark:text-orange-400 font-medium hover:underline"
        >
          Watch Now →
        </a>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    initializeFAQs();
    setupSearch();
    setupFilters();
    setupKeyboardShortcuts();
  });

  function initializeFAQs() {
    // FAQ Accordion functionality with enhanced animations
    document.querySelectorAll(".faq-question").forEach((button) => {
      button.addEventListener("click", () => {
        const answer = button.nextElementSibling;
        const icon = button.querySelector(".fas");

        // Toggle current answer
        answer.classList.toggle("hidden");
        icon.classList.toggle("rotate-180");

        // Smooth scroll to question if opening
        if (answer.classList.contains("hidden") === false) {
          setTimeout(() => {
            button.scrollIntoView({ behavior: "smooth", block: "nearest" });
          }, 100);
        }
      });
    });
  }

  function setupSearch() {
    const searchInput = document.getElementById("faqSearch");
    const faqItems = document.querySelectorAll(".faq-item");
    const faqCategories = document.querySelectorAll(".faq-category");
    const noResults = document.getElementById("noResults");
    const searchResults = document.getElementById("searchResults");
    const resultCount = document.getElementById("resultCount");

    searchInput.addEventListener("input", function (e) {
      const searchTerm = e.target.value.toLowerCase().trim();
      let visibleCount = 0;

      if (searchTerm === "") {
        // Show all items and categories
        faqItems.forEach((item) => {
          item.style.display = "block";
          visibleCount++;
        });
        faqCategories.forEach((category) => {
          category.style.display = "block";
        });
        noResults.classList.add("hidden");
        searchResults.classList.add("hidden");
        return;
      }

      // Search through FAQ items
      faqItems.forEach((item) => {
        const question = item
          .querySelector(".faq-question h3")
          .textContent.toLowerCase();
        const answer = item
          .querySelector(".faq-answer p")
          .textContent.toLowerCase();

        if (question.includes(searchTerm) || answer.includes(searchTerm)) {
          item.style.display = "block";
          visibleCount++;

          // Highlight search terms
          highlightSearchTerm(item, searchTerm);
        } else {
          item.style.display = "none";
        }
      });

      // Hide categories that have no visible items
      faqCategories.forEach((category) => {
        const visibleItems = category.querySelectorAll(
          '.faq-item[style*="block"]'
        );
        if (visibleItems.length === 0) {
          category.style.display = "none";
        } else {
          category.style.display = "block";
        }
      });

      // Update search results counter
      resultCount.textContent = visibleCount;
      searchResults.classList.remove("hidden");

      // Show no results message if needed
      if (visibleCount === 0) {
        noResults.classList.remove("hidden");
      } else {
        noResults.classList.add("hidden");
      }
    });
  }

  function highlightSearchTerm(item, searchTerm) {
    const question = item.querySelector(".faq-question h3");
    const answer = item.querySelector(".faq-answer p");

    // Remove previous highlights
    question.innerHTML = question.textContent;
    answer.innerHTML = answer.textContent;

    if (searchTerm.length > 2) {
      const regex = new RegExp(`(${searchTerm})`, "gi");
      question.innerHTML = question.textContent.replace(
        regex,
        '<mark class="bg-yellow-200 dark:bg-yellow-600">$1</mark>'
      );
      answer.innerHTML = answer.textContent.replace(
        regex,
        '<mark class="bg-yellow-200 dark:bg-yellow-600">$1</mark>'
      );
    }
  }

  function setupFilters() {
    const filterTags = document.querySelectorAll(".filter-tag");
    const faqCategories = document.querySelectorAll(".faq-category");
    const searchInput = document.getElementById("faqSearch");

    filterTags.forEach((tag) => {
      tag.addEventListener("click", () => {
        const filter = tag.dataset.filter;

        // Update active filter styling
        filterTags.forEach((t) => {
          t.classList.remove("bg-blue-500", "text-white");
          t.classList.add(
            "bg-white",
            "dark:bg-gray-800",
            "text-gray-700",
            "dark:text-gray-300"
          );
        });
        tag.classList.remove(
          "bg-white",
          "dark:bg-gray-800",
          "text-gray-700",
          "dark:text-gray-300"
        );
        tag.classList.add("bg-blue-500", "text-white");

        // Clear search
        searchInput.value = "";
        document.getElementById("searchResults").classList.add("hidden");
        document.getElementById("noResults").classList.add("hidden");

        // Filter categories
        if (filter === "all") {
          faqCategories.forEach((category) => {
            category.style.display = "block";
            category.querySelectorAll(".faq-item").forEach((item) => {
              item.style.display = "block";
            });
          });
        } else {
          faqCategories.forEach((category) => {
            if (category.dataset.category === filter) {
              category.style.display = "block";
              category.querySelectorAll(".faq-item").forEach((item) => {
                item.style.display = "block";
              });
            } else {
              category.style.display = "none";
            }
          });
        }

        // Smooth scroll to top of FAQ container
        document.getElementById("faqContainer").scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      });
    });

    // Set default active filter
    filterTags[0].click();
  }

  function setupKeyboardShortcuts() {
    const searchInput = document.getElementById("faqSearch");

    // Ctrl+K to focus search
    document.addEventListener("keydown", function (e) {
      if (e.ctrlKey && e.key === "k") {
        e.preventDefault();
        searchInput.focus();
        searchInput.select();
      }

      // Escape to clear search
      if (e.key === "Escape" && document.activeElement === searchInput) {
        searchInput.value = "";
        searchInput.dispatchEvent(new Event("input"));
        searchInput.blur();
      }
    });

    // Enter to expand first result
    searchInput.addEventListener("keydown", function (e) {
      if (e.key === "Enter") {
        const firstVisibleQuestion = document.querySelector(
          '.faq-item[style*="block"] .faq-question'
        );
        if (firstVisibleQuestion) {
          firstVisibleQuestion.click();
        }
      }
    });
  }

  // Live chat function (if not already defined)
  function openLiveChat() {
    // Redirect to support page or open chat modal
    window.location.href = "{{ url_for('support') }}#chat";
  }
</script>

<style>
  /* Enhanced animations and transitions */
  .faq-item {
    transition: all 0.3s ease;
  }

  .faq-item:hover {
    transform: translateY(-2px);
  }

  .faq-question {
    transition: all 0.2s ease;
  }

  .faq-answer {
    transition: all 0.3s ease;
  }

  .filter-tag {
    transition: all 0.2s ease;
  }

  .filter-tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  /* Search highlighting */
  mark {
    padding: 2px 4px;
    border-radius: 3px;
  }

  /* Smooth scrolling for the entire page */
  html {
    scroll-behavior: smooth;
  }

  /* Loading animation for search */
  #faqSearch:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
</style>
{% endblock %}
